package com.wosai.upay.job.service.task;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.job.enume.ErrorMsgViewEndpointTypeEnum;
import com.wosai.upay.job.enume.microUpgrade.UpgradeVersion;
import com.wosai.upay.job.model.MerchantAcquireInfoBO;
import com.wosai.upay.job.model.dto.crm.CheckResultForCrmInfoManageDTO;
import com.wosai.upay.job.model.dto.crm.CrmInformationManagementApplyFormDTO;
import com.wosai.upay.job.model.dto.crm.SubmitResultForCrmInfoManageDTO;
import com.wosai.upay.job.model.dto.crm.v3.MicroUpgradeDTO;
import com.wosai.upay.job.model.dto.microUpgrade.BusinessLicenseCertificationProcess;
import com.wosai.upay.job.model.dto.request.BusinessLicenseUpdateDTO;
import com.wosai.upay.job.model.dto.request.LicenseUpdateAmountVerifyReqDTO;
import com.wosai.upay.job.model.dto.request.UpdateCardInfoAfterMicroUpgradeDTO;
import com.wosai.upay.job.model.dto.response.AlreadyDeletedParamsByMicroUpgradeRspDTO;
import com.wosai.upay.job.model.dto.response.CuaCommonResultDTO;
import com.wosai.upay.job.model.dto.response.InsertLicenceUpdateTaskResultRspDTO;
import com.wosai.upay.job.model.dto.response.LicenseUpdateAmountVerifyResDTO;
import org.springframework.validation.annotation.Validated;
import vo.ApiRequestParam;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 营业执照任务服务
 *
 * <AUTHOR>
 * @date 2024/9/5 17:11
 */
@JsonRpcService("/rpc/task/licence")
@Validated
public interface BusinessLicenceTaskService {

    /**
     * 当前商户是否有营业执照更新任务
     * 对于审批单处理中的校验，不可以调用该方法，因为该方法会判断审批单处理中的商户
     *
     * @param merchantSn 商户号
     *  @return 是否有营业执照更新任务
     */
    Boolean hasLicenceUpdateNotFinishedTask(String merchantSn);

    /**
     * 当前商户是否有营业执照更新任务v2
     *
     * @param merchantSn 商户号
     * @return 结果
     */
    Boolean hasLicenseUpdateNotFinishedV2Task(String merchantSn);


    /**
     * 营业执照处理v1
     * 新增营业执照更新(认证）任务
     * 任务内容：重新进件（如果是小微升级），通知风控， 更新营业执照信息（还有涉及银行卡）
     *
     * @param updateReqDTO 营业执照更新请求
     * @return 响应结果
     */
    InsertLicenceUpdateTaskResultRspDTO insertLicenceUpdateTask(BusinessLicenseUpdateDTO updateReqDTO);


    /**
     * 针对收钱吧非小微商户，新增营业执照更新(认证）任务
     *
     * @param merchantSn 商户号
     * @return 响应结果
     */
    InsertLicenceUpdateTaskResultRspDTO insertLicenceUpdateTaskForNotMicroInSQB(String merchantSn);

    /**
     * 批量针对收钱吧非小微商户，新增营业执照更新(认证）任务
     *
     * @param merchantSns 商户号列表
     */
    void batchInsertLicenceUpdateTasksForNotMicroInSQB(List<String> merchantSns);

    /**
     * 风控通知人工审核结果
     *
     * @param manualAuditPass 人工审核是否通过 true-通过
     * @param merchantSn      商户号
     */
    void notifyRiskManualLicenceAuditResult(String merchantSn, boolean manualAuditPass);


    /**
     * 判断商户是否需要重新进件
     *
     * @param merchantSn 商户号
     * @return 是否需要进件 true-需要
     */
    Boolean isUpgradeNeedContractToAcquirer(String merchantSn);


    /**
     * 商户是否正在做小微升级
     *
     * @param merchantSn 商户号
     * @return 是否正在做小微升级 true-是
     */
    Boolean isMerchantDoingMicroUpgrade(String merchantSn);

    /**
     * 是否可以提交已禁用商户换卡审批
     * 如果不可以，抛出异常（适配审批中心接口标准）
     *
     * @param paramsMap 参数
     */
    void isCanSubmitDisableMerchantChangeCardAudit(Map paramsMap);

    /**
     * 查询由于小微升级而被逻辑删除的交易参数
     *
     * @param merchantSn 商户号
     * @return 删除的参数列表
     */
    List<AlreadyDeletedParamsByMicroUpgradeRspDTO> listDeletedParamsByMicroUpgrade(String merchantSn);


    /**
     * 针对小微升级后，修改原商户号的结算卡信息
     *
     * @param updateCardInfoAfterMicroUpgradeDTO 小微升级后，修改原商户号的结算卡信息请求
     * @return 结果
     */
    CuaCommonResultDTO updateCardInfoAfterMicroUpgrade(UpdateCardInfoAfterMicroUpgradeDTO updateCardInfoAfterMicroUpgradeDTO);


    /**
     * 内部洗数据接口
     * 存量商户重新触发小微升级
     * 对于拉卡拉：收钱吧非小微，lkl侧是小微
     * 对于富友：触发小微升级 （富友的背景是什么，需要做校验吗）
     *
     * @param merchantSn 商户号
     * @param acquirer   收单机构
     * @return 结果
     */
    CuaCommonResultDTO internalTriggerMicroUpgrade(String merchantSn, String acquirer);


    /**
     * 文件解析-小微升级
     *
     * @param fileUrl    文件地址
     * @return 结果
     */
    CuaCommonResultDTO internalTriggerMicroUpgradeByFile(String fileUrl);

    /**
     * 内部洗数据接口
     * 存量商户重新触发小微升级
     *
     * @param merchantSn 商户号
     * @return 结果
     */
    CuaCommonResultDTO internalTriggerMicroUpgrade(String merchantSn);



    /**
     * 临时接口 更新营业执照-法人是执行事务合伙人 目前只支持拉卡拉
     * 法人证件类型改成执行事务合伙人上送拉卡拉：13
     * 法人证件号改成商户执照号
     * 法人证件姓名改成执行事务合伙人后面的公司（不用加委派代表）
     *
     * @param merchantSn          商户号
     * @param providerMerchantId  渠道商户号
     * @param legalPersonName     法人名称（执行事务合伙人后面的公司（不用加委派代表））
     * @param legalPersonIdNumber 法人证件号
     * @return 执行结果
     */
    CuaCommonResultDTO updateBusinessLicenceForExecutivePartner(String merchantSn, String providerMerchantId,
                                                                String legalPersonName, String legalPersonIdNumber);


    ///  营业执照认证v2...............

    /**
     * 校验30天是否存在营业执照变更更新结算名称成功的任务
     *
     * @param merchantSn 商户号
     * @return 校验寄过
     */
    Boolean isLicenseUpdateExistSuccessBusinessNameChange(String merchantSn);

    /**
     * 营业执照认证审批单校验
     * 收单机构校验
     *
     * @param applyReq 营业执照审批单
     * @return 校验结果
     */
    CheckResultForCrmInfoManageDTO checkSubmitBusinessLicenseApplyToContractSideAudit(CrmInformationManagementApplyFormDTO applyReq);

    /**
     * 营业执照认证审批单提交进件侧审核
     *
     * @param applyReq 营业执照审批单
     * @return 处理结果
     */
    SubmitResultForCrmInfoManageDTO submitBusinessLicenseApplyToContractSideAudit(CrmInformationManagementApplyFormDTO applyReq);


    /**
     * 营业执照更新-crm 用户验证金额
     *
     * @param req req
     * @return 验证结果
     */
    LicenseUpdateAmountVerifyResDTO verifyAmountForCrmUpdateLicense(@Valid ApiRequestParam<LicenseUpdateAmountVerifyReqDTO> req);

    /**
     * 营业执照更新-app 用户验证金额
     * 需要通知进件侧，从而更新审批单
     *
     * @param verifyReqDTO req
     * @return 验证结果
     */
    LicenseUpdateAmountVerifyResDTO verifyAmountForAppUpdateLicense(@Valid LicenseUpdateAmountVerifyReqDTO verifyReqDTO);

    /**
     * 营业执照变更-修改经营名称
     * 此接口调用时机在营业执照变更已经完成
     *
     * @param merchantSn    商户号
     * @param businessName  经营名称
     */
    void updateBusinessNameInLicenseCertificate(String merchantSn, String businessName);


    /**
     * 小微升级已删除参数，更新禁用原因
     *
     * @param merchantSn 商户号
     */
    void updateDisableReasonForMicroUpgradeDeletedParams(String merchantSn);


    /**
     * 当前商户路由到小微升级V3
     * @param merchantSn 商户号
     * @return
     */
    Boolean isMicroUpgradeV3(String merchantSn);

    /**
     * 根据配置决定商户小微升级任务的版本
     * @return 版本标识字符串 (e.g., "V2", "V3")
     */
    UpgradeVersion determineMicroUpgradeVersion(MicroUpgradeDTO dto);



    /**
     * 当前商户是否有营业执照更新任务v3
     *
     * @param merchantSn 商户号
     * @return 结果
     */
    Boolean hasLicenseUpdateNotFinishedV3Task(String merchantSn);


    /**
     * 小微升级V3对应的业务信息上下文
     *
     * @param merchantSn 商户号
     * @return 结果F
     */
    Map<String,Object> getNotFinishedV3TaskContext(String merchantSn);



    /**
     * 营业执照认证流程
     *
     * @param merchantSn 商户号
     * @param endpointType 平台(根据不同平台获取提示信息)
     *
     * @return 结果
     */
    List<BusinessLicenseCertificationProcess> showBusinessLicenseCertificationProcess(String merchantSn, ErrorMsgViewEndpointTypeEnum endpointType);


    /**
     * 获取当前收单机构对应的小微升级数据
     * @param merchantSn
     * @return
     */
    MerchantAcquireInfoBO getMerchantAcquireInfoBO(String merchantSn);




    /**
     * 获取商户切换参数时间
     * @param merchantSn 商户号
     * @return
     */
    String changeParamsTime(String merchantSn);








}
