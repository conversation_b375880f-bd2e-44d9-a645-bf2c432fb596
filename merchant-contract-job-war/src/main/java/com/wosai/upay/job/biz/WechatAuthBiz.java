package com.wosai.upay.job.biz;


import com.alibaba.fastjson.JSONObject;
import com.shouqianba.cua.enums.core.BusinessLicenseTypeEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.PayMchAuthStatusEnum;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.bank.info.api.service.IndustryV2Service;
import com.wosai.upay.common.exception.CommonPubBizException;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.core.model.MerchantBusinessLicence;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.ContractChannel;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.util.CommonUtil;
import com.wosai.upay.merchant.contract.exception.ContractException;
import com.wosai.upay.merchant.contract.model.weixin.MchAuthResp;
import com.wosai.upay.merchant.contract.service.AuthApplyFlowService;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class WechatAuthBiz {

    @Autowired
    private MerchantProviderParamsMapper paramsMapper;

    @Autowired
    private AuthApplyFlowService authApplyFlowService;

    @Autowired
    private RuleContext ruleContext;
    @Autowired
    ParamContextBiz paramContextBiz;

    @Autowired
    private IndustryV2Service industryV2Service;
    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;
    @Autowired
    private IndustryMappingCommonBiz industryMappingCommonBiz;

    public static final String AUTHORIZE_STATE_AUTHORIZED = "AUTHORIZE_STATE_AUTHORIZED";

    public static final String WEIXIN_INDIRECT_INDUSTRY = "weixin_indirect_industry";
    public static final String WEIXIN_ONLINE_SETTLEMENT_ID = "weixin_online_settlement_id";

    public static final int NO_INDUSTRY_INFO = 461;
    public static final int NO_SETTLEID = 462;

    /**
     * 查询子商户号授权状态
     *
     * @param merchantProviderParams
     * @param provider
     * @return
     */
    public Boolean getAuthStatus(MerchantProviderParams merchantProviderParams, Integer provider) {
        if (Objects.equals(PayMchAuthStatusEnum.YES.getValue(), merchantProviderParams.getAuth_status())) {
            return true;
        }
        return getAuthStatusImmediately(merchantProviderParams.getPay_merchant_id(), merchantProviderParams.getChannel_no(), merchantProviderParams.getId(), provider);
    }

    public Boolean getAuthStatusImmediately(String wxSubMchId, String channelNo, String paramId, Integer provider) {
        MchAuthResp authResp;
        try {
            ContractChannel channel = ruleContext.getContractChannel(PaywayEnum.WEIXIN.getValue(), String.valueOf(provider), channelNo);
            authResp = authApplyFlowService.queryAuthStatus(wxSubMchId, channel.buildAuthV3Param());
        } catch (Exception e) {
            log.error("查询子商户号授权状态异常: ", e);
            throw new CommonPubBizException(e.getMessage());
        }
        Boolean authed = AUTHORIZE_STATE_AUTHORIZED.equals(authResp.getAuthorize_state()) ? Boolean.TRUE : Boolean.FALSE;
        if (authed) {
            paramsMapper.updateByPrimaryKeySelective(
                    new MerchantProviderParams()
                            .setId(paramId)
                            .setAuth_status(PayMchAuthStatusEnum.YES.getValue())
                            .setMtime(System.currentTimeMillis())
            );
        }
        return authed;
    }


    /**
     * 获取微信子商户的授权状态
     * @param wxSubMchId
     * @param channel
     * @return
     */
    public Boolean getAuthStatusImmediately(String wxSubMchId, String channel) {
        MchAuthResp authResp;
        try {
            ContractChannel contractChannel = ruleContext.getContractChannel(channel);
            authResp = authApplyFlowService.queryAuthStatus(wxSubMchId, contractChannel.buildAuthV3Param());
        } catch (Exception e) {
            log.error("查询子商户号授权状态异常: ", e);
            throw new CommonPubBizException(e.getMessage());
        }
        return AUTHORIZE_STATE_AUTHORIZED.equals(authResp.getAuthorize_state()) ? Boolean.TRUE : Boolean.FALSE;
    }


    public Boolean getAuthStatus(MerchantProviderParamsDO providerParamsDO, Integer provider) {
        if (Objects.equals(PayMchAuthStatusEnum.YES.getValue(), providerParamsDO.getAuthStatus())) {
            return true;
        }
        return getAuthStatusImmediately(providerParamsDO.getPayMerchantId(), providerParamsDO.getChannelNo(), providerParamsDO.getId(), provider);
    }


    public WechatAuthNameAndSettId getMerchantNameAndSettlementId(String merchantSn) {
        Map<String, Object> context = paramContextBiz.getParamContextByMerchantSn(merchantSn, null);
        return getMerchantNameAndSettlementId(context);
    }

    public WechatAuthNameAndSettId getMerchantNameAndSettlementId(Map<String, Object> context) {
        String merchantName = getWechatAuthMerchantName(context);
        Integer type = (Integer) BeanUtil.getNestedProperty(context, "merchantBusinessLicense.type");
        String industry = (String) BeanUtil.getNestedProperty(context, "merchant.industry");
        String settlementId = getSettlementId(industry, type, merchantName);

        return new WechatAuthNameAndSettId()
                .setMerchantName(merchantName)
                .setSettlementId(settlementId);
    }

    /**
     * 获取微信实名认证的商户名
     *
     * @param merchantSn
     * @return
     */
    public String getWechatAuthMerchantName(String merchantSn) {
        Map<String, Object> context = paramContextBiz.getParamContextByMerchantSn(merchantSn, null);
        return getWechatAuthMerchantName(context);
    }

    public String getWechatAuthMerchantName(Map context) {
        Map<String, Object> merchantBankAccount = (Map) context.get(ParamContextBiz.KEY_BANK_ACCOUNT);
        Map<String, Object> business = (Map) context.get(ParamContextBiz.KEY_BUSINESS_LICENCE);
        Boolean forceMicro = (Boolean) context.getOrDefault(ParamContextBiz.FORCE_MICRO, Boolean.FALSE);
        int type = BeanUtil.getPropInt(business, MerchantBusinessLicence.TYPE, BusinessLicenseTypeEnum.MICRO.getValue());
        String merchantName;
        if (type == BusinessLicenseTypeEnum.MICRO.getValue() || forceMicro) {
            merchantName = "商户_" + CommonUtil.substring(BeanUtil.getPropString(merchantBankAccount, MerchantBankAccount.HOLDER), 50);
        } else {
            merchantName = CommonUtil.substring(BeanUtil.getPropString(business, MerchantBusinessLicence.NAME), 50);
        }
        return merchantName.trim();
    }

    public String getSettlementId(String industryId, int type, String merchantName) {
        Map industryMapping = getSettlementConfig(industryId);
        // 3 根据证照类型获取对应的结算id
        String key = getSubjectTypeByLicenseType(type, merchantName);
        String settlementId = MapUtils.getString(industryMapping, key);
        if (WosaiStringUtils.isEmpty(settlementId)) {
            log.info(String.format("匹配不到微信结算id:%s,%d,%s", industryId, type, merchantName));
            throw new ContractException(NO_SETTLEID, "根据商户行业和商户类型匹配不到微信结算id");
        }
        return settlementId;
    }

    public String getSettlementIdIgnoreNotFound(String industryId, int type, String merchantName) {
        Map industryMapping = getSettlementConfig(industryId);
        // 3 根据证照类型获取对应的结算id
        String key = getSubjectTypeByLicenseType(type, merchantName);
        String settlementId = MapUtils.getString(industryMapping, key);
        if (WosaiStringUtils.isEmpty(settlementId)) {
            log.info(String.format("匹配不到微信结算id:%s,%d,%s", industryId, type, merchantName));
            return null;
        }
        return settlementId;
    }

    public String getOnlineSettlementId(int licenseType, String merchantName, boolean forceMicro) {
        String key = getSubjectTypeByLicenseType(licenseType, merchantName);
        if (forceMicro) {
            key = "person";
        }
        Map onlineSettlementIdConfig = applicationApolloConfig.getWeixinOnlineSettlementId();
        return org.apache.commons.collections4.MapUtils.getString(onlineSettlementIdConfig, key);
    }

    public Map getSettlementConfig(String industryId) {
        return JSONObject.parseObject(JSONObject.toJSONString(industryMappingCommonBiz.getSettlementConfig(industryId)), Map.class);
    }

    private String getSubjectTypeByLicenseType(int type, String merchantName) {
        String subjectType;
        if (type == BusinessLicenseTypeEnum.MICRO.getValue()) {
            subjectType = "person";
        } else if (type == BusinessLicenseTypeEnum.ENTERPRISE.getValue()
                || (type == BusinessLicenseTypeEnum.INDIVIDUAL.getValue() && WosaiStringUtils.isNotEmpty(merchantName) && merchantName.endsWith("公司"))
                || type == BusinessLicenseTypeEnum.COOPERATIVE.getValue()) {
            subjectType = "enterprise";
        } else if (type == BusinessLicenseTypeEnum.INDIVIDUAL.getValue()) {
            subjectType = "individual";
        } else if (type == BusinessLicenseTypeEnum.INSTITUTIONAL.getValue() || type == BusinessLicenseTypeEnum.UNIFORM.getValue()) {
            subjectType = "institutions";
        } else {
            subjectType = "others";
        }
        return subjectType;
    }

    @Data
    @Accessors(chain = true)
    public static class WechatAuthNameAndSettId {

        private String merchantName;

        private String settlementId;

    }
}
