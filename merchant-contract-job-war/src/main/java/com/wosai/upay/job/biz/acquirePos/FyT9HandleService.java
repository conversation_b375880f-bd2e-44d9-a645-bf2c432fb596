package com.wosai.upay.job.biz.acquirePos;

import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Maps;
import com.google.common.collect.Lists;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.model.StoreInfo;
import com.wosai.mc.service.StoreService;
import com.wosai.trade.service.request.ApplyFeeRateRequest;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.Terminal;
import com.wosai.upay.core.model.TerminalConfig;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.Constants.PosConstant;
import com.wosai.upay.job.Constants.PreAuthApplyConstant;
import com.wosai.upay.job.Constants.TradeConstants;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.biz.ProviderTerminalBiz;
import com.wosai.upay.job.biz.acquirer.IAcquirerBiz;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.job.enume.PlatformEnum;
import com.wosai.upay.job.enume.ProviderTerminalBindConfigStatus;
import com.wosai.upay.job.externalservice.coreb.TradeConfigClient;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.dto.AcquirerMerchantDto;
import com.wosai.upay.job.model.dto.acquirePos.*;
import com.wosai.upay.job.model.lklV3Pos.ApplyPosRequest;
import com.wosai.upay.job.refactor.dao.ProviderTerminalBindConfigDAO;
import com.wosai.upay.job.refactor.model.entity.ProviderTerminalBindConfigDO;
import com.wosai.upay.merchant.contract.model.LimitResp;
import com.wosai.upay.merchant.contract.model.fuyou.response.ElecContractSignV2StatusQueryResponse;
import com.wosai.upay.merchant.contract.model.fuyou.response.ModifyCancelResponse;
import com.wosai.upay.merchant.contract.model.fuyou.response.TermAddResponse;
import com.wosai.upay.merchant.contract.model.fuyou.response.TermQueryResponse;
import com.wosai.upay.merchant.contract.model.provider.FuyouParam;
import com.wosai.upay.merchant.contract.service.FuyouService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2024/2/26 18:01
 */
@Component
@Slf4j
public class FyT9HandleService extends AbstractT9HandleService {
    @Autowired
    private FuyouService fuyouService;

    @Autowired
    private ProviderTerminalBiz providerTerminalBiz;

    @Autowired
    private ContractParamsBiz contractParamsBiz;

    @Autowired
    private ProviderTerminalBindConfigDAO providerTerminalBindConfigDAO;
    @Autowired
    private StoreService storeService;

    @Autowired
    private TradeConfigClient tradeConfigClient;

    protected String FUYOU_TRADE_PARAMS = "fuyou_trade_params";

    protected String TERMINAL_ID = "terminal_id";
    protected String TERM_ID = "term_id";

    protected String PROVIDER_MCH_ID = "provider_mch_id";

    public static final String FY_AGENT_NAME = "1038_21_*_false_true_0001";

    public static final String C2B_FEE_RATE = "0.55";

    @Value("${fy_pso_dev_code}")
    public String fyPsoDevCode;

    @Value("${fyForeignCard.devCode}")
    private String fyForeignCardDevCode;


    @Value("${fy.preAuth.devCode}")
    private String fyPreauthDevcode;

    /**
     * 富友电子签约号
     */
    private String CONTRACT_NO = "contractNo";
    @Override
    public String choseAcquire() {
        return McConstant.ACQUIRER_FUYOU;
    }



    @Override
    public void innerBindCheck(InnerBindCheckDTO dto) {
        MerchantInfo merchant = merchantService.getMerchantBySn(dto.getMerchantSn(), null);
        String merchantSn = merchant.getSn();
        final String merchantId = merchant.getId();
        List<AcquirerMerchantDto> acquirerMerchantInfo = merchantProviderParamsService.getAcquirerMerchantInfo(merchantId);
        //是否成功进件
        boolean match = acquirerMerchantInfo.parallelStream().anyMatch(info -> info.getAcquirer().contains(McConstant.ACQUIRER_FUYOU));
        if(!match) {
            throw new CommonPubBizException(PosConstant.FAIL_OTHER_ACQUIRE);
        }
        //当前正在使用的收单机构
        ContractStatus contractStatus = Optional.ofNullable(contractStatusService.selectByMerchantSn(merchantSn))
                .orElseGet(ContractStatus::new);
        String acquirer = contractStatus.getAcquirer();
        if(Objects.equals(acquirer,McConstant.ACQUIRER_FUYOU)) {
            List<String> otherAcquireVenderList = factory.otherAcquireVenderList(McConstant.ACQUIRER_FUYOU);
            existOtherT9(merchantId,otherAcquireVenderList);
            return;
        }
        List<String> indirectAcquirerList = mcAcquirerDAO.getIndirectAcquirerList();
        if(indirectAcquirerList.contains(acquirer)) {
            throw new CommonPubBizException(PosConstant.FAIL_OTHER_ACQUIRE);
        }
        //当前在银行通道
        List<String> otherAcquireVenderList = factory.otherAcquireVenderList(McConstant.ACQUIRER_FUYOU);
        existOtherT9(merchantId,otherAcquireVenderList);
        return;
    }

    @Override
    public ContractResponse openPos(ApplyPosRequest request) {
        final ContractResponse response = new ContractResponse();
        final String merchantSn = request.getMerchantSn();
        try {
            //先插入状态信息crm可以立刻查询到
            directStatusBiz.createOrUpdateDirectStatus(merchantSn, request.getDevCode(), DirectStatus.STATUS_PROCESS, null);
            final String formBody = request.getForm_body();
            final Map map = JSONObject.parseObject(formBody, Map.class);
            //保存电子协议
            CompletableFuture.runAsync(()->saveAgreement(merchantSn,map));
            MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_FUYOU.getValue());
            final String payMerchantId = acquirerParams.getPay_merchant_id();
            //推送消息
            directStatusBiz.createOrUpdateDirectStatus(merchantSn, request.getDevCode(), DirectStatus.STATUS_SUCCESS, null);
            //TODO 调用交易接口初始化信息
            updateFuyouCardMerchantConfig(request, map, payMerchantId);
            response.setSuccess(Boolean.TRUE);
        } catch (Exception exception) {
            log.error("applyPos error request:{},exception:{}", JSONObject.toJSONString(request), exception);
            directStatusBiz.createOrUpdateDirectStatus(merchantSn, request.getDevCode(), DirectStatus.STATUS_BIZ_FAIL, exception.getMessage());
            response.setMsg(exception.getMessage());
        }
        return response;
    }

    private void saveAgreement(String merchantSn,Map map) {
        com.wosai.upay.merchant.contract.model.ContractResponse signV2StatusQuery = fuyouService.elecContractSignV2StatusQuery(merchantSn, BeanUtil.getPropString(map, CONTRACT_NO));
        ElecContractSignV2StatusQueryResponse elecQuery = JSONObject.parseObject(
                JSONObject.toJSONString(Optional.ofNullable(signV2StatusQuery.getResponseParam()).orElseGet(HashMap::new)),
                ElecContractSignV2StatusQueryResponse.class);
        String signPdfUrl = elecQuery.getSignPdfUrl();
        if(StringUtils.isEmpty(signPdfUrl)) {
            return;
        }
        MerchantInfo merchant = merchantService.getMerchantBySn(merchantSn, null);
        agreementBiz.recordAgreementForT9(merchant.getId(),signPdfUrl);
    }


    /**
     * 交易参数
     *
     * @param request
     * @param map
     * @param providerMerchantId 银联商户号
     */
    public void updateFuyouCardMerchantConfig(ApplyPosRequest request, Map map, String providerMerchantId) {
        final Map<String, Map> bankcardFee = MapUtils.getMap(map,BANKCARD_FEE);
        final long tradeComboId = BeanUtil.getPropLong(map, TRADECOMBO_ID);
        final String merchantSn = request.getMerchantSn();
        final MerchantInfo merchant = merchantService.getMerchantBySn(merchantSn, null);
        final Map merchantConfigParams = CollectionUtil.hashMap(MerchantConfig.MERCHANT_ID,merchant.getId(),
                MerchantConfig.C2B_STATUS,MerchantConfig.STATUS_OPENED,
                //兜底费率
                MerchantConfig.C2B_FEE_RATE,C2B_FEE_RATE,
                MerchantConfig.C2B_AGENT_NAME,FY_AGENT_NAME,
                MerchantConfig.PROVIDER,PayParamsModel.PROVIDER_FUYOU,
                MerchantConfig.PARAMS,CollectionUtil.hashMap(FUYOU_TRADE_PARAMS, CollectionUtil.hashMap(PROVIDER_MCH_ID, providerMerchantId),BANKCARD_FEE,bankcardFee)
                );
        //交易参数管理
        //TODO 调用交易接口初始化信息
        tradeConfigService.updateBankCardMerchantConfig(merchantConfigParams);
        t9PosFeeCombo(bankcardFee, tradeComboId, merchantSn);
        //记录在sub_biz_param表中
        String t9TradeAppId = subBizParamsBiz.getT9TradeAppId();
        subBizParamsBiz.updateSubBizParams(merchantSn, t9TradeAppId, PayParamsModel.PROVIDER_FUYOU, new MerchantProviderParams());
    }

    /**
     * 设置T9刷卡费率套餐
     * @param bankcardFee
     * @param tradeComboId
     * @param merchantSn
     */
    public void t9PosFeeCombo(Map<String, Map> bankcardFee, long tradeComboId, String merchantSn) {
        try {
            final ArrayList<Map> list = Lists.newArrayList();
            bankcardFee.forEach((k, v) -> {
                final Map detailMap = CollectionUtil.hashMap("type", String.valueOf(k).toLowerCase(), "fee", BeanUtil.getPropString(v, "fee"));
                final String maxFee = BeanUtil.getPropString(v, "max");
                if (StringUtils.isNotBlank(maxFee)) {
                    detailMap.put("max", maxFee);
                }
                list.add(detailMap);
            });
            Map bankPosMap = CollectionUtil.hashMap("fee_rate_type", "channel", "value", list);

            final Map applyFeeRateMap = CollectionUtil.hashMap(
                    "21", JSONObject.toJSONString(bankPosMap));
            ApplyFeeRateRequest applyFeeRateRequest = new ApplyFeeRateRequest();
            applyFeeRateRequest.setMerchantSn(merchantSn);
            applyFeeRateRequest.setAuditSn("T9业务开通");
            applyFeeRateRequest.setTradeComboId(tradeComboId);
            applyFeeRateRequest.setApplyPartialPayway(Boolean.FALSE);
            applyFeeRateRequest.setApplyFeeRateMap(applyFeeRateMap);
            applyFeeRateRequest.setCheck(Boolean.TRUE);
            feeRateService.applyFeeRateOne(applyFeeRateRequest);
            supportService.removeCachedParams(merchantSn);
        } catch (Exception e) {
            log.error("t9PosFeeCombo error:{}",e);
        }
    }

    /**
     * 获取POS机激活信息
     *
     * @param dto 包含POS终端序列号的DTO对象
     * @return 返回PosActiveInfo对象，包含激活后的信息；如果激活失败或无法激活，则返回空信息。
     * @throws CommonPubBizException 如果激活检查不通过或过程出错，则抛出异常。
     */
    @Override
    public PosActiveInfo getPosActiveInfo(PosActiveInfoDTO dto) {
        // 根据传入的终端序列号获取终端信息
        String terminalSn = dto.getTerminalSn();
        Map terminal = terminalService.getTerminalBySn(terminalSn);
        // 从终端信息中提取商家ID，并根据商家ID获取商家信息
        String merchantId = BeanUtil.getPropString(terminal, Terminal.MERCHANT_ID);
        MerchantInfo merchant = merchantService.getMerchantById(merchantId, null);
        String merchantSn = merchant.getSn();
        // 查询银行卡开通状态，如果状态不是成功，则抛出异常
        DirectStatus directStatus = Optional.ofNullable(directStatusBiz.getDirectStatusByMerchantSnAndDevCode(merchantSn, fyPsoDevCode))
                .orElseGet(DirectStatus::new);
        if(!Objects.equals(DirectStatus.STATUS_SUCCESS,directStatus.getStatus())) {
            throw new CommonPubBizException(PosConstant.APPLY_OPEN);
        }
        // 当前使用的收单机构
        ContractStatus contractStatus = Optional.ofNullable(contractStatusService.selectByMerchantSn(merchantSn))
                .orElseGet(ContractStatus::new);
        String acquirer = contractStatus.getAcquirer();
        List<String> indirectAcquirerList = mcAcquirerDAO.getIndirectAcquirerList();
        //间连非富友
        if(indirectAcquirerList.contains(acquirer) && !Objects.equals(acquirer,McConstant.ACQUIRER_FUYOU)) {
            //当前不是富友，则抛出无法激活的异常
            throw new CommonPubBizException(PosConstant.CAN_NOT_ACTIVE);
        }

        // 调用富友服务进行终端添加
        com.wosai.upay.merchant.contract.model.ContractResponse response = fuyouService.termQuery(merchantSn, terminalSn);
        if(!response.isSuccess()){
            // 如果富友服务调用失败，则抛出异常
            throw new CommonPubBizException(response.getMessage());
        }
        // 处理富友服务返回的响应数据
        Map<String, Object> respData = Optional.ofNullable(response.getResponseParam()).orElseGet(HashMap::new);
        TermQueryResponse termQueryResponse = JSONObject.parseObject(JSONObject.toJSONString(respData), TermQueryResponse.class);
        String acquireTermId = termQueryResponse.getTermId();
        if(StringUtils.isEmpty(acquireTermId)) {
            // 如果不存在有效的激活信息，则抛出异常
            throw new CommonPubBizException(PosConstant.ACTIVE_NO_FAIL);
        }
        PosActiveInfo posActiveInfo = new PosActiveInfo();
        // 更新终端配置，将终端号写入交易
        updateFuYouCardTerminalConfig(BeanUtil.getPropString(terminal,DaoConstants.ID),acquireTermId);
        posActiveInfo.setActiveNo(acquireTermId);
        //开通多业务的时候写入新的清算通道
        try {
            tradeConfigClient.updateClearProviderByAcquirer(merchantId, McConstant.ACQUIRER_FUYOU);
        } catch (Exception e) {
            log.error("清算通道设置失败 商户号:{}",merchantSn,e);
            throw new CommonPubBizException("清算通道设置失败");
        }
        return posActiveInfo;
    }

    private void updateFuYouCardTerminalConfig(String terminalId, String acquireTermId) {
        final Map terminalConfigParams = CollectionUtil.hashMap(TERMINAL_ID,terminalId,
                TerminalConfig.PAYWAY, TradeConstants.PAYWAY_BANKCARD,
                TerminalConfig.C2B_STATUS,TerminalConfig.STATUS_OPENED,
                TerminalConfig.C2B_FEE_RATE,C2B_FEE_RATE,
                TerminalConfig.C2B_AGENT_NAME,FY_AGENT_NAME,
                TerminalConfig.PROVIDER,PayParamsModel.PROVIDER_FUYOU);
        if(!StringUtils.isEmpty(acquireTermId)) {
            terminalConfigParams.put(MerchantConfig.PARAMS,CollectionUtil.hashMap(FUYOU_TRADE_PARAMS, CollectionUtil.hashMap(TERM_ID, acquireTermId)));
        }else {
            terminalConfigParams.put(MerchantConfig.PARAMS, Maps.newHashMap());
        }
        try {
            tradeConfigService.updateBankCardTerminalConfig(terminalConfigParams);
        } catch (Exception e) {
            log.error("updateFuYouCardTerminalConfig acquireTermId:{},error:{}",acquireTermId,e);
        }
    }

    @Override
    public void fyTermCancel(UnbindDTO dto)  {
        final String merchantId = dto.getMerchantId();
        MerchantInfo merchant = merchantService.getMerchantById(merchantId, null);
        String merchantSn = merchant.getSn();
        com.wosai.upay.merchant.contract.model.ContractResponse response = fuyouService.termCancel(merchantSn, dto.getDeviceFingerprint());
        if(!response.isSuccess()) {
           throw new CommonPubBizException(response.getMessage());
        }
        //将交易侧"获取激活码时写入的终端号"删除
        updateFuYouCardTerminalConfig(dto.getTerminalId(),null);
    }

    @Override
    public void t9TermAdd(String terminalSn) throws CommonPubBizException {
        Map terminal = terminalService.getTerminalBySn(terminalSn);
        // 从终端信息中提取商家ID，并根据商家ID获取商家信息
        String merchantId = BeanUtil.getPropString(terminal, Terminal.MERCHANT_ID);
        MerchantInfo merchant = merchantService.getMerchantById(merchantId, null);
        String merchantSn = merchant.getSn();
        // 调用富友服务进行终端添加
        com.wosai.upay.merchant.contract.model.ContractResponse response = fuyouService.termAdd(merchantSn, terminalSn);
        if(!response.isSuccess()){
            // 如果富友服务调用失败，则抛出异常
            throw new CommonPubBizException(response.getMessage());
        }
        // 处理富友服务返回的响应数据
        Map<String, Object> respData = Optional.ofNullable(response.getResponseParam()).orElseGet(HashMap::new);
        TermAddResponse termAddResponse = JSONObject.parseObject(JSONObject.toJSONString(respData), TermAddResponse.class);
        String acquireTermId = termAddResponse.getTermId();
        if(StringUtils.isEmpty(acquireTermId)) {
            // 如果富友服务调用失败，则抛出异常
            throw new CommonPubBizException("终端号不存在");
        }
        //更新到trade_extra_config(注意由于富友刷卡POS在银行卡支付和扫码支付使用的是同一个收单机构终端号,所以这样处理)
        providerTerminalBiz.createOrUpdateTerminalExtra(merchantSn,
                acquireTermId,
                PayParamsModel.PROVIDER_FUYOU,
                BeanUtil.getPropString(terminal,Terminal.VENDOR_APP_APPID),
                terminalSn);
        //将记录插入到provider_terminal_bind_config
        insertProviderTermBindConfig(terminalSn, terminal, merchantSn, acquireTermId);
    }


    /**
     * 插入商户终端绑定关系
     * @param terminalSn
     * @param terminal
     * @param merchantSn
     * @param acquireTermId
     */
    private void insertProviderTermBindConfig(String terminalSn, Map terminal, String merchantSn, String acquireTermId) {
        try {
            String storeId = BeanUtil.getPropString(terminal, Terminal.STORE_ID);
            final StoreInfo store = storeService.getStoreById(storeId, null);
            ProviderTerminalBindConfigDO providerTerminalBindConfigDO = new ProviderTerminalBindConfigDO();
            providerTerminalBindConfigDO.setId(UUID.randomUUID().toString());
            providerTerminalBindConfigDO.setMerchantSn(merchantSn);
            providerTerminalBindConfigDO.setStoreSn(store.getSn());
            providerTerminalBindConfigDO.setTerminalSn(terminalSn);
            providerTerminalBindConfigDO.setProvider(ProviderEnum.PROVIDER_FUYOU.getValue());
            providerTerminalBindConfigDO.setProviderTerminalId(acquireTermId);
            providerTerminalBindConfigDO.setPayway(PaywayEnum.BANK_CARD.getValue());
            providerTerminalBindConfigDO.setStatus(ProviderTerminalBindConfigStatus.BIND_SUCCESS.getStatus());
            providerTerminalBindConfigDAO.insertOne(providerTerminalBindConfigDO);
        } catch (Exception e) {
            log.error("{} provider_terminal_bind_config插入失败", terminalSn,e);
        }
    }

    @Override
    public ModifyCancelResponse fyModifyCancel(String merchantSn, String modifyNo) {
        final com.wosai.upay.merchant.contract.model.ContractResponse response = fuyouService.modifyCancel(merchantSn, modifyNo);
        final Map<String, Object> responseParam = Optional.ofNullable(response.getResponseParam()).orElseGet(HashMap::new);
        final ModifyCancelResponse cancelResponse = JSONObject.parseObject(JSONObject.toJSONString(responseParam), ModifyCancelResponse.class);
        return cancelResponse;
    }

    @Override
    public List<LimitResp> queryLimit(String merchantSn) {
        final String providerMerchantId = Optional.ofNullable(merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_FUYOU.getValue()))
                .map(MerchantProviderParams::getProvider_merchant_id)
                .orElse(null);
        //还没有进件成功
        if (org.apache.commons.lang3.StringUtils.isEmpty(providerMerchantId)) {
            return new ArrayList<>();
        }
        final FuyouParam fuyouParam = contractParamsBiz.buildContractParams(ChannelEnum.FUYOU.getValue(), FuyouParam.class);
        return fuyouService.queryLimit(providerMerchantId, fuyouParam);
    }

    @Override
    public String getDevCode() {
        return this.fyPsoDevCode;
    }

    public boolean isSupport(String devCode) {
        return Objects.equals(this.fyPsoDevCode, devCode);
    }

    @Override
    public void closeForeignCard(String merchantSn) {
        //删除外卡记录
        final ForeignCard foreignCard = Optional.ofNullable(foreignCardMapper.selectSuccessByMerchantSnAndCode(merchantSn,fyForeignCardDevCode))
                .orElseGet(ForeignCard::new);
        if(Objects.nonNull(foreignCard.getId())) {
            foreignCardMapper.deleteByPrimaryKey(foreignCard.getId());
        }
        directStatusDAO.deleteByMerchantSnAndDevCode(merchantSn,fyForeignCardDevCode);


    }


    @Override
    public void closePreAuth(String merchantSn) {
        final PreAuthApply preAuthApply = preAuthApplyMapper.selectPreAuthApply(merchantSn, fyPreauthDevcode);
        Optional.ofNullable(preAuthApply)
                .filter(apply -> Objects.equals(apply.getStatus(), PreAuthApplyConstant.Status.SUCCESS))
                .ifPresent(apply -> preAuthApplyMapper.deleteByPrimaryKey(apply.getId()));

        if (merchantSn != null && fyPreauthDevcode != null) {
            directStatusDAO.deleteByMerchantSnAndDevCode(merchantSn, fyPreauthDevcode);
        }
    }
}
