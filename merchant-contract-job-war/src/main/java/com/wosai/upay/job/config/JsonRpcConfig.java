package com.wosai.upay.job.config;


import com.googlecode.jsonrpc4j.InvocationListener;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImplExporter;
import com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean;
import com.shouqianba.campus.center.application.CampusZoneService;
import com.shouqianba.service.ContractRelatedMappingConfigService;
import com.shouqianba.service.MerchantContractService;
import com.shouqianba.service.MerchantProviderParamsService;
import com.shouqianba.service.TerminalManagementService;
import com.shouqianba.workflow.service.AuditService;
import com.shouqianba.workflow.service.CallBackService;
import com.wosai.agreement.service.AgreementService;
import com.wosai.aop.gateway.service.ClientSideNoticeService;
import com.wosai.aop.gateway.service.ClientSidePushService;
import com.wosai.aop.gateway.service.ClientSideSmsService;
import com.wosai.app.backend.api.service.IAccountService;
import com.wosai.app.push.api.service.IPushMessageService;
import com.wosai.app.service.AuthCodeService;
import com.wosai.app.service.MerchantUserService;
import com.wosai.app.service.v2.UcUserAccountService;
import com.wosai.assistant.service.UserRpcService;
import com.wosai.biz.merchantlevel.service.MerchantActiveLevelService;
import com.wosai.bsm.creditpaybackend.service.FitnessNotifyService;
import com.wosai.bsm.creditpaybackend.service.FqConfigService;
import com.wosai.bsm.financebackend.service.ProviderChangeService;
import com.wosai.business.log.service.BizObjectColumnService;
import com.wosai.business.log.service.BizOpLogService;
import com.wosai.business.log.service.BusinessLogService;
import com.wosai.core.crypto.service.CryptoService;
import com.wosai.cua.brand.business.api.facade.BrandFacade;
import com.wosai.data.crow.api.service.OnlineQueryService;
import com.wosai.data.crow.api.service.TagIngestService;
import com.wosai.device.service.SimpleSmartPosInfoService;
import com.wosai.market.boss.circle.user.api.UserFollowAndFansService;
import com.wosai.merchant.service.IBalanceService;
import com.wosai.merchant.service.IStockMerchantService;
import com.wosai.notice.service.NoticeService;
import com.wosai.operation.activity.service.BlueseaService;
import com.wosai.operation.activity.service.WechantOasisService;
import com.wosai.operator.service.BusinessOpenService;
import com.wosai.profit.sharing.service.SharingConfigService;
import com.wosai.profit.sharing.service.SharingOpenService;
import com.wosai.pub.alipay.authinto.service.AlipayStoreService;
import com.wosai.pub.alipay.authinto.service.StoreService;
import com.wosai.risk.service.IRiskBlistSceneService;
import com.wosai.risk.service.RiskOrderGenerateService;
import com.wosai.sales.core.service.IKeeperService;
import com.wosai.sales.core.service.IMerchantService;
import com.wosai.sales.core.service.OrganizationService;
import com.wosai.sales.core.service.UserService;
import com.wosai.sales.merchant.business.service.common.CommonAppInfoService;
import com.wosai.sales.merchant.business.service.common.CommonFieldService;
import com.wosai.sales.merchant.business.service.common.CommonMerchantInfoService;
import com.wosai.sales.merchant.business.service.common.TimeTaskService;
import com.wosai.sales.service.MerchantEnrolService;
import com.wosai.sales.service.goDe.GaoDeService;
import com.wosai.sales.todo.service.TodoDetailService;
import com.wosai.service.*;
import com.wosai.shouqianba.withdrawservice.service.WithdrawService;
import com.wosai.sp.business.logstash.service.BusinessOpLogService;
import com.wosai.task.service.TaskInstanceService;
import com.wosai.trade.service.*;
import com.wosai.trade.service.bank.BankFeeRateService;
import com.wosai.upay.bank.info.api.service.IndustryService;
import com.wosai.upay.bank.info.api.service.*;
import com.wosai.upay.bank.service.*;
import com.wosai.upay.bank.service.MerchantBizBankAccountService;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.core.service.*;
import com.wosai.upay.core.service.TerminalService;
import com.wosai.upay.job.config.rpc.ActivitySystemService;
import com.wosai.upay.job.exception.JobDefaultErrorResolver;
import com.wosai.upay.job.externalservice.trademanage.service.MyApplyActivityService;
import com.wosai.upay.job.refactor.service.rpc.bank.AccountBankAccountService;
import com.wosai.upay.job.refactor.service.rpc.bank.BankAccountVerifyService;
import com.wosai.upay.job.refactor.service.rpc.bank.BankLicenseUpdateChangeCardService;
import com.wosai.upay.job.refactor.service.rpc.coreb.CoreBTradeConfigService;
import com.wosai.upay.job.refactor.service.rpc.crm.open.CrmCommonFieldService;
import com.wosai.upay.job.refactor.service.rpc.risk.RiskMerchantBusinessLicenseAuditService;
import com.wosai.upay.job.service.rpc.ClearanceService;
import com.wosai.upay.job.service.rpc.PayBusinessOpenApplyManageService;
import com.wosai.upay.job.service.rpc.QldMerchantInfoService;
import com.wosai.upay.job.util.RpcConfigUtil;
import com.wosai.upay.lkl.service.PayNotifyService;
import com.wosai.upay.merchant.audit.api.service.IndirectPayAuditService;
import com.wosai.upay.merchant.audit.api.service.MerchantBusinessLicenseAuditService;
import com.wosai.upay.merchant.contract.service.*;
import com.wosai.upay.merchant.contract.service.PingAnService;
import com.wosai.upay.qrcode.service.QrcodeService;
import com.wosai.upay.remit.service.KafkaService;
import com.wosai.upay.remit.service.RemitOrderService;
import com.wosai.upay.side.service.GeneralRuleService;
import com.wosai.upay.transaction.service.IAccountBookService;
import com.wosai.upay.transaction.service.TransactionServiceV2;
import com.wosai.upay.wallet.service.WalletService;
import facade.ICustomerRelationFacade;
import facade.ICustomerRelationValidateFacade;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * Created by lihebin on 2018/6/1.
 */

@Configuration
public class JsonRpcConfig {


    @Value("${jsonrpc.merchant_contract}")
    private String merchantContractUrl;

    @Value("${jsonrpc.core_business}")
    private String coreBusinessUrl;

    @Value("${jsonrpc.notice_service}")
    private String noticeServiceUrl;

    @Value("${jsonrpc.app_push_service}")
    private String appPushServiceUrl;

    @Value("${jsonrpc.app_backend_service}")
    private String appBackendServiceUrl;

    @Value("${jsonrpc.business_log}")
    private String businessLogUrl;

    @Value("${jsonrpc.business_log_stash.server}")
    private String businessLogStashUrl;

    @Value("${jsonrpc.opr_merchant_activity}")
    private String oprMerchantActivity;

    @Value("${jsonrpc.shouqianba_merchant.server}")
    private String shouqianbaMerchantServiceUrl;

    @Value("${jsonrpc.crm}")
    private String crmUrl;

    @Value("${jsonrpc.sales-system-poi}")
    private String salesSystemPoiUrl;

    @Value("${jsonrpc.remit.order.server}")
    private String remitOrderUrl;

    @Value("${jsonrpc.upay.side.server}")
    private String upaySideUrl;

    @Value("${jsonrpc.bank-info.server}")
    private String bankInfoUrl;

    @Value("${jsonrpc.merchant-bank.server}")
    private String bankServiceUrl;

    @Value("${jsonrpc.merchant-audit.server}")
    private String merchantAuditUrl;

    @Value("${jsonrpc.merchant-enrolment.server}")
    private String merchantEnrolmentUrl;

    @Value("${jsonrpc.risk-disposal.server}")
    private String riskDisposalUrl;

    @Value("${jsonrpc.trade_manage}")
    private String tradeManageUrl;

    @Value("${jsonrpc.upay-wallet}")
    private String upayWalletUrl;

    @Value("${jsonrpc.withdraw-service}")
    private String withdrawServiceUrl;

    @Value("${jsonrpc.finance-backend}")
    private String financeBackendUrl;

    @Value("${jsonrpc.alipay}")
    private String alipayUrl;

    @Value("${jsonrpc.profit-sharing}")
    private String profitSharingUrl;

    @Value("${jsonrpc.merchant_user_service}")
    private String merchantUserServiceUrl;

    @Value("${jsonrpc.aop_gate}")
    private String aopGateUrl;

    @Value("${jsonrpc.sp-workflow-service}")
    private String spWorkflowServiceUrl;

    @Value("${jsonrpc.merchant-center}")
    private String merchantCenterUrl;

    @Value("${jsonrpc.crow}")
    private String crowUrl;

    @Value("${jsonrpc.qrcode}")
    private String qrcodeUrl;

    @Value("${jsonrpc.crm-customer-relation}")
    private String crmCustomerRelationUrl;

    @Value("${jsonrpc.upay-transaction}")
    private String upayTransactionUrl;

    @Value("${jsonrpc.pay-business-open}")
    private String payBusinessOpenUrl;

    @Value("${jsonrpc.core-crypto}")
    private String coreCryptoUrl;

    @Value("${jsonprc.authcode-service}")
    private String authcodeServiceUrl;

    @Value("${jsonrpc.sp-task}")
    private String spTaskUrl;

    @Value("${jsonrpc.contract-activity}")
    private String contractActivityUrl;

    @Value("${jsonrpc.merchant_level}")
    private String merchantLevelUrl;

    @Value("${jsonrpc.boss-circle-user}")
    private String bossCircleUserUrl;

    @Value("${jsonrpc.agreement-manage}")
    private String agreementManageUrl;
    @Value("${jsonrpc.merchant-business-open}")
    private String merchantBusinessOpenUrl;

    @Value("${jsonrpc.campus-center}")
    private String campusCenterUrl;

    @Value("${jsonrpc.credit-pay-backend}")
    private String creditPayBackendUrl;

    @Value("${jsonrpc.crm-todo-api}")
    private String crmTodoUrl;

    @Value(("${jsonrpc.sales-system-backend}"))
    private String salesSystemBackendUrl;

    @Value("${jsonrpc.risk-service}")
    private String riskServiceUrl;

    @Value("${jsonrpc.merchant-contract-access}")
    private String merchantContractAccessUrl;

    @Value("${jsonrpc.brand-business}")
    private String brandBusinessUrl;

    @Value("${jsonrpc.lakala-proxy}")
    private String lklProxyUrl;

    @Value("${jsonrpc.dc-service}")
    private String dcServiceUrl;

    @Value("${jsonrpc.jupiter}")
    private String jupiterUrl;

    @Value("${jsonrpc.partner-payment-hub}")
    private String partnerPaymentHubUrl;

    @Bean
    public JsonProxyFactoryBean payNotifyService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(lklProxyUrl + "/rpc/lkl", PayNotifyService.class);
    }

    @Bean
    public JsonProxyFactoryBean contractRelatedMappingConfigService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(merchantContractAccessUrl + "/rpc/config/mapping", ContractRelatedMappingConfigService.class);
    }

    @Bean
    public JsonProxyFactoryBean accessMerchantContractService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(merchantContractAccessUrl + "/rpc/merchant/contract", MerchantContractService.class);
    }

    @Bean
    public JsonProxyFactoryBean accessMerchantProviderParamsService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(merchantContractAccessUrl + "/rpc/merchantProviderParams", MerchantProviderParamsService.class);
    }

    @Bean
    public static AutoJsonRpcServiceImplExporter autoJsonRpcServiceImplExporter(InvocationListener invocationListener) {
        AutoJsonRpcServiceImplExporter exp = new AutoJsonRpcServiceImplExporter();
        exp.setErrorResolver(new JobDefaultErrorResolver());
        exp.setInvocationListener(invocationListener);
        return exp;
    }

    @Bean
    public JsonProxyFactoryBean userRpcService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(salesSystemBackendUrl + "/rpc/user", UserRpcService.class);
    }

    @Bean
    public JsonProxyFactoryBean unionOpenService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingMerchantContractBean(merchantContractUrl + "/rpc/union_open", UnionOpenService.class);
    }

    @Bean
    public JsonProxyFactoryBean composeService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingMerchantContractBean(merchantContractUrl + "/rpc/compose", ComposeService.class);
    }

    @Bean
    public JsonProxyFactoryBean haikeService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingMerchantContractBean(merchantContractUrl + "/rpc/haike", HaikeService.class);
    }

    @Bean
    public JsonProxyFactoryBean fuyouService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingMerchantContractBean(merchantContractUrl + "/rpc/fuyou", FuyouService.class);
    }

    @Bean
    public JsonProxyFactoryBean lklV3Service() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingMerchantContractBean(merchantContractUrl + "/rpc/lklV3", LklV3Service.class);
    }

    @Bean
    public JsonProxyFactoryBean lakalaService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingMerchantContractBean(merchantContractUrl + "/rpc/lakala", LakalaService.class);
    }

    @Bean
    public JsonProxyFactoryBean aliPayDirectService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingMerchantContractBean(merchantContractUrl + "/rpc/aliPayDirectService", AliPayDirectService.class);
    }

    @Bean
    public JsonProxyFactoryBean weiXinDirectService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingMerchantContractBean(merchantContractUrl + "/rpc/weiXinDirect", WeiXinDirectService.class);
    }

    @Bean
    public JsonProxyFactoryBean newLakalaService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingMerchantContractBean(merchantContractUrl + "/rpc/newlakala", NewLakalaService.class);
    }

    @Bean
    public JsonProxyFactoryBean guangFaService() {
        JsonProxyFactoryBean jsonProxyFactoryBean = RpcConfigUtil.getJsonProxyFactoryWareTracingMerchantContractBean(merchantContractUrl + "/rpc/guangfa", GuangFaService.class);
        jsonProxyFactoryBean.setReadTimeoutMillis(90000);
        jsonProxyFactoryBean.setConnectionTimeoutMillis(90000);
        return jsonProxyFactoryBean;
    }

    @Bean
    public JsonProxyFactoryBean uploadService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingMerchantContractBean(merchantContractUrl + "/rpc/upload", UploadService.class);
    }

    @Bean
    public JsonProxyFactoryBean iMerchantService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(crmUrl + "/rpc/merchant", IMerchantService.class);
    }

    @Bean
    public JsonProxyFactoryBean UserService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(crmUrl + "/rpc/user", UserService.class);
    }

    @Bean
    public JsonProxyFactoryBean organizationService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(crmUrl + "/rpc/organization", OrganizationService.class);
    }

    @Bean
    public JsonProxyFactoryBean iKeeperService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(crmUrl + "/rpc/keeper", IKeeperService.class);
    }

    @Bean
    public JsonProxyFactoryBean gaoDeService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(salesSystemPoiUrl + "/rpc/gaoDe", GaoDeService.class);
    }

    @Bean
    public JsonProxyFactoryBean unionService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingMerchantContractBean(merchantContractUrl + "/rpc/union", UnionService.class);
    }

    @Bean
    public JsonProxyFactoryBean AliPayAuthApplyFlowService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingMerchantContractBean(merchantContractUrl + "/rpc/alipayAuthApply", AliPayAuthApplyFlowService.class);
    }

    @Bean
    public JsonProxyFactoryBean providerTradeParamsService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingMerchantContractBean(merchantContractUrl + "/rpc/providerTradeParams", ProviderTradeParamsService.class);
    }

    @Bean
    public JsonProxyFactoryBean weixinService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingMerchantContractBean(merchantContractUrl + "/rpc/weixin", WeixinService.class);
    }

    @Bean
    public JsonProxyFactoryBean tongLianService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingMerchantContractBean(merchantContractUrl + "/rpc/tonglian", TongLianService.class);
    }

    @Bean
    public JsonProxyFactoryBean tonglianV2Service() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingMerchantContractBean(merchantContractUrl + "/rpc/tonglian_v2", TongLianV2Service.class);
    }

    @Bean
    public JsonProxyFactoryBean merchantService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(coreBusinessUrl + "/rpc/merchant", MerchantService.class);
    }


    @Bean
    public JsonProxyFactoryBean merchantBankService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(coreBusinessUrl + "/rpc/merchantBank", MerchantBankService.class);
    }

    @Bean
    public JsonProxyFactoryBean accountVerifyService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(bankServiceUrl + "/rpc/account_verify", AccountVerifyService.class);
    }

    @Bean
    public JsonProxyFactoryBean bankAccountVerifyService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(bankServiceUrl + "/rpc/account_verify", BankAccountVerifyService.class);
    }

    @Bean
    public JsonProxyFactoryBean tradeConfigService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(coreBusinessUrl + "/rpc/tradeConfig", TradeConfigService.class);
    }

    @Bean
    public JsonProxyFactoryBean rsaKeyService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(coreBusinessUrl + "/rpc/rsaKey", RsaKeyService.class);
    }

    @Bean
    public JsonProxyFactoryBean coreBTradeConfigService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(coreBusinessUrl + "/rpc/tradeConfig", CoreBTradeConfigService.class);
    }


    @Bean
    public JsonProxyFactoryBean supportService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(coreBusinessUrl + "/rpc/support", SupportService.class);
    }

    @Bean
    public JsonProxyFactoryBean businssCommonService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(coreBusinessUrl + "/rpc/common", BusinssCommonService.class);
    }

    @Bean
    public JsonProxyFactoryBean merchantBusinessLicenseService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(coreBusinessUrl + "/rpc/merchant_business_license", MerchantBusinessLicenseService.class);
    }


    @Bean
    public JsonProxyFactoryBean noticeService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(noticeServiceUrl + "/rpc/notice", NoticeService.class);
    }

    @Bean
    public JsonProxyFactoryBean iPushMessageService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(appPushServiceUrl + "/rpc/pushMessage", IPushMessageService.class);
    }


    @Bean
    public JsonProxyFactoryBean iAccountService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(appBackendServiceUrl + "/rpc/account", IAccountService.class);
    }

    @Bean
    public JsonProxyFactoryBean bizOpLogService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(businessLogUrl + "/rpc/opLog", BizOpLogService.class);
    }

    @Bean
    public JsonProxyFactoryBean bizObjectColumnService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(businessLogUrl + "/rpc/objectColumn", BizObjectColumnService.class);
    }

    @Bean
    public JsonProxyFactoryBean businessLogService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(businessLogUrl + "/rpc/businessLog", BusinessLogService.class);
    }

    @Bean
    public JsonProxyFactoryBean businessOpLogService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(businessLogStashUrl + "/rpc/businessOpLog", BusinessOpLogService.class);
    }

    @Bean
    public JsonProxyFactoryBean wechantOasisService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(oprMerchantActivity + "/rpc/wechant/oasis", WechantOasisService.class);
    }

    @Bean
    public JsonProxyFactoryBean blueseaService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(oprMerchantActivity + "/rpc/bluesea", BlueseaService.class);
    }

    @Bean
    public JsonProxyFactoryBean iStockMerchantService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(shouqianbaMerchantServiceUrl + "/rpc/stock-merchant", IStockMerchantService.class);
    }

    @Bean
    public JsonProxyFactoryBean iBalanceService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(shouqianbaMerchantServiceUrl + "/rpc/balance", IBalanceService.class);
    }

    @Bean
    public JsonProxyFactoryBean industryV2Service() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(bankInfoUrl + "/rpc/industry_v2", IndustryV2Service.class);
    }

    @Bean
    public JsonProxyFactoryBean bankInfoService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(bankInfoUrl + "/rpc/bankinfo", BankInfoService.class);
    }

    @Bean
    public JsonProxyFactoryBean industryService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(bankInfoUrl + "/rpc/industry", IndustryService.class);
    }

    @Bean
    public JsonProxyFactoryBean bankInfoDistrictService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(bankInfoUrl + "/rpc/districts", DistrictsService.class);
    }

    @Bean
    public JsonProxyFactoryBean industryConfigService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(bankInfoUrl + "/rpc/industryConfig", IndustryConfigService.class);
    }

    @Bean
    public JsonProxyFactoryBean rmqService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(coreBusinessUrl + "/rpc/rmq", RMQService.class);
    }

    @Bean
    public JsonProxyFactoryBean newUnionService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingMerchantContractBean(merchantContractUrl + "/rpc/newUnion", NewUnionService.class);
    }

    @Bean
    public JsonProxyFactoryBean blueSeaService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingMerchantContractBean(merchantContractUrl + "/rpc/bluesea", BlueSeaService.class);
    }

    @Bean
    public JsonProxyFactoryBean newBlueSeaService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingMerchantContractBean(merchantContractUrl + "/rpc/new_blue_sea", NewBlueSeaService.class);
    }

    @Bean
    public JsonProxyFactoryBean nuccService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingMerchantContractBean(merchantContractUrl + "/rpc/nuccServcie", NuccServcie.class);
    }

    @Bean
    public JsonProxyFactoryBean authApplyFlowService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingMerchantContractBean(merchantContractUrl + "/rpc/authApply", AuthApplyFlowService.class);
    }


    @Bean
    public JsonProxyFactoryBean remitOrderService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(remitOrderUrl + "/rpc/order", RemitOrderService.class);
    }

    @Bean
    public JsonProxyFactoryBean kafkaService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(remitOrderUrl + "/rpc/kafka", KafkaService.class);
    }

    @Bean
    public JsonProxyFactoryBean generalRuleService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(upaySideUrl + "/rpc/rule", GeneralRuleService.class);
    }

    @Bean
    public JsonProxyFactoryBean bankService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(bankServiceUrl + "/rpc/merchantBank", BankService.class);
    }

    //这个改为商户中心吧
    @Bean
    public JsonProxyFactoryBean bankBusinessLicenseService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(bankServiceUrl + "/rpc/bank_business_license", BankBusinessLicenseService.class);
    }

    @Bean
    public JsonProxyFactoryBean merchantBizBankAccountService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(bankServiceUrl + "/rpc/merchantBizBankAccount", MerchantBizBankAccountService.class);
    }

    @Bean
    public JsonProxyFactoryBean logService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(coreBusinessUrl + "/rpc/log", LogService.class);
    }

    @Bean
    public JsonProxyFactoryBean auditService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(merchantAuditUrl + "/rpc/merchantAudit", com.wosai.upay.merchant.audit.api.service.MerchantAuditService.class);
    }

    @Bean
    public JsonProxyFactoryBean indirectPayAuditService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(merchantAuditUrl + "/rpc/indirectPayAudit", IndirectPayAuditService.class);
    }

    @Bean
    public JsonProxyFactoryBean merchantBusinessLicenseAuditService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(merchantAuditUrl + "/rpc/businessLicenseAudit", MerchantBusinessLicenseAuditService.class);
    }

    @Bean
    public JsonProxyFactoryBean riskMerchantBusinessLicenseAuditService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(merchantAuditUrl + "/rpc/businessLicenseAudit", RiskMerchantBusinessLicenseAuditService.class);
    }


    @Bean
    public JsonProxyFactoryBean merchantEnrolService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(merchantEnrolmentUrl + "/rpc/enrol", MerchantEnrolService.class);
    }

    @Bean
    public JsonProxyFactoryBean riskOrderGenerateService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(riskDisposalUrl + "/rpc/generate", RiskOrderGenerateService.class);
    }

    @Bean
    public JsonProxyFactoryBean tradeComboService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(tradeManageUrl + "/rpc/trade_combo", TradeComboService.class);
    }

    @Bean
    public JsonProxyFactoryBean tradeComboDetailService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(tradeManageUrl + "/rpc/trade_combo_detail", TradeComboDetailService.class);
    }

    @Bean
    public JsonProxyFactoryBean tradeAppService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(tradeManageUrl + "/rpc/trade_app", TradeAppService.class);
    }

    @Bean
    public JsonProxyFactoryBean tradeStateService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(tradeManageUrl + "/rpc/trade_state", TradeStateService.class);
    }

    @Bean
    public JsonProxyFactoryBean switchService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(tradeManageUrl + "/rpc/switch", SwitchService.class);
    }

    @Bean
    public JsonProxyFactoryBean walletService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(upayWalletUrl + "/rpc/wallet", WalletService.class);
    }

    @Bean
    public JsonProxyFactoryBean withdrawService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(withdrawServiceUrl + "/rpc/withdraw", WithdrawService.class, 2000, 5000);
    }

    @Bean
    public JsonProxyFactoryBean providerChangeService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(financeBackendUrl + "/rpc/providerChange", ProviderChangeService.class);
    }

    @Bean
    public JsonProxyFactoryBean storeService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(alipayUrl + "/rpc/store", StoreService.class);
    }

    @Bean
    public JsonProxyFactoryBean alipayStoreService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(alipayUrl + "/rpc/alipaystore", AlipayStoreService.class);
    }

    @Bean
    public JsonProxyFactoryBean sqbStoreService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(coreBusinessUrl + "/rpc/store", com.wosai.upay.core.service.StoreService.class);
    }

    @Bean
    public JsonProxyFactoryBean bankNamesService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(bankInfoUrl + "/rpc/bankNames", BankNamesService.class);
    }

    @Bean
    public JsonProxyFactoryBean sharingConfigService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(profitSharingUrl + "/rpc/sharingConfig", SharingConfigService.class);
    }

    @Bean
    public JsonProxyFactoryBean SharingOpenService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(profitSharingUrl + "/rpc/open", SharingOpenService.class, 5000, 30000);
    }

    @Bean
    public JsonProxyFactoryBean merchantUserService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(merchantUserServiceUrl + "/rpc/merchantuser", MerchantUserService.class);
    }

    @Bean
    public JsonProxyFactoryBean ucUserAccountService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(merchantUserServiceUrl + "/rpc/ucUser", UcUserAccountService.class);
    }

    @Bean
    public JsonProxyFactoryBean clientSideNoticeService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(aopGateUrl + "/rpc/clientSide/notice", ClientSideNoticeService.class);
    }

    @Bean
    public JsonProxyFactoryBean clientSidePushService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(aopGateUrl + "/rpc/clientSide/push", ClientSidePushService.class);
    }

    @Bean
    public JsonProxyFactoryBean clientSideSmsService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(aopGateUrl + "/rpc/clientSide/sms", ClientSideSmsService.class);
    }

    @Bean
    public JsonProxyFactoryBean CallBackService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(spWorkflowServiceUrl + "/rpc/callback", CallBackService.class);
    }

    @Bean
    public JsonProxyFactoryBean AuditService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(spWorkflowServiceUrl + "/rpc/audit", AuditService.class);
    }

    @Bean
    public JsonProxyFactoryBean mcMerchantService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(merchantCenterUrl + "/rpc/merchant", com.wosai.mc.service.MerchantService.class);
    }

    @Bean
    public JsonProxyFactoryBean mcMerchantBusinessLicenseService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(merchantCenterUrl + "/rpc/merchant_business_license", com.wosai.mc.service.MerchantBusinessLicenseService.class);
    }

    @Bean
    public JsonProxyFactoryBean mcStoreService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(merchantCenterUrl + "/rpc/store", com.wosai.mc.service.StoreService.class);
    }

    @Bean
    public JsonProxyFactoryBean mcStoreExtService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(merchantCenterUrl + "/rpc/storeExt", com.wosai.mc.service.StoreExtService.class);
    }

    @Bean
    public JsonProxyFactoryBean terminalService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(coreBusinessUrl + "/rpc/terminal", TerminalService.class);
    }

    @Bean
    public JsonProxyFactoryBean tagIngestService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(crowUrl + "/rpc/tag_ingests", TagIngestService.class);
    }

    @Bean
    public JsonProxyFactoryBean onlineQueryService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(crowUrl + "/rpc/online_queries", OnlineQueryService.class);
    }

    @Bean
    public JsonProxyFactoryBean mchFeeRateService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(tradeManageUrl + "/rpc/mchFeeRate", FeeRateService.class);
    }

    @Bean
    public JsonProxyFactoryBean cuaSupportService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(tradeManageUrl + "/rpc/cuaSupport", CuaSupportService.class);
    }

    @Bean
    public JsonProxyFactoryBean psbcService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingMerchantContractBean(merchantContractUrl + "/rpc/psbc_service", PsbcService.class);
    }


    @Bean
    public JsonProxyFactoryBean umsService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingMerchantContractBean(merchantContractUrl + "/rpc/ums", ChinaUmsService.class);
    }

    @Bean
    public JsonProxyFactoryBean ccbService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingMerchantContractBean(merchantContractUrl + "/rpc/ccb_service", CcbService.class);
    }

    @Bean
    public JsonProxyFactoryBean guotongService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingMerchantContractBean(merchantContractUrl + "/rpc/guotong", GuotongService.class);
    }

    @Bean
    public JsonProxyFactoryBean hxService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingMerchantContractBean(merchantContractUrl + "/rpc/hx_bank", HXService.class);
    }

    @Bean
    public JsonProxyFactoryBean qrcodeService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(qrcodeUrl + "/rpc/qrcode", QrcodeService.class);
    }

    @Bean
    public JsonProxyFactoryBean iCustomerRelationFacade() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(crmCustomerRelationUrl + "/rpc/relation", ICustomerRelationFacade.class);
    }

    @Bean
    public JsonProxyFactoryBean iAccountBookService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(upayTransactionUrl + "/rpc/account_book_v4", IAccountBookService.class);
    }

    @Bean
    public JsonProxyFactoryBean transactionServiceV2() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(upayTransactionUrl + "/rpc/transaction_v2", TransactionServiceV2.class);
    }

    @Bean
    public JsonProxyFactoryBean crmEdgeService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(payBusinessOpenUrl + "/rpc/crmEdgeService", com.wosai.upay.service.CrmEdgeService.class);
    }

    @Bean
    public JsonProxyFactoryBean cryptoService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(coreCryptoUrl + "/rpc/crypto", CryptoService.class);
    }

    @Bean
    public JsonProxyFactoryBean authCodeService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(authcodeServiceUrl + "/rpc/authCode", AuthCodeService.class);
    }


    @Bean
    public JsonProxyFactoryBean taskInstanceService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(spTaskUrl + "/rpc/taskInstance", TaskInstanceService.class);
    }

    @Bean
    public JsonProxyFactoryBean wxStoreService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(contractActivityUrl + "/rpc/wx_store", WxStoreService.class);
    }

    @Bean
    public JsonProxyFactoryBean alipayAuthService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(contractActivityUrl + "/rpc/alipayAuth", AlipayAuthService.class);
    }


    @Bean
    public JsonProxyFactoryBean wechatAuthService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(contractActivityUrl + "/rpc/wechatAuth", WechatAuthService.class);
    }

    @Bean
    public JsonProxyFactoryBean merchantActiveLevelService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(merchantLevelUrl + "/rpc/merchantactivelevel", MerchantActiveLevelService.class);
    }

    @Bean
    public JsonProxyFactoryBean customerRelationValidateFacade() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(crmCustomerRelationUrl + "/rpc/relationvalidate", ICustomerRelationValidateFacade.class);
    }

    @Bean
    public JsonProxyFactoryBean snGenerator() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(coreBusinessUrl + "/rpc/sn", SnGenerator.class);
    }

    @Bean
    public JsonProxyFactoryBean districtsServiceV2() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(bankInfoUrl + "/rpc/districtsv2", DistrictsServiceV2.class);
    }

    @Bean
    public JsonProxyFactoryBean applyActivityService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(tradeManageUrl + "/rpc/applyActivity", ApplyActivityService.class);
    }

    @Bean
    public JsonProxyFactoryBean MyApplyActivityService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(tradeManageUrl + "/rpc/applyActivity", MyApplyActivityService.class);
    }

    @Bean
    public JsonProxyFactoryBean userFollowAndFansService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(bossCircleUserUrl + "/rpc/follow", UserFollowAndFansService.class);
    }

    @Bean
    public JsonProxyFactoryBean agreementService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(agreementManageUrl + "/rpc/agreement", AgreementService.class);
    }

    @Bean
    public JsonProxyFactoryBean commonMerchantInfoService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(merchantBusinessOpenUrl + "/rpc/common/merchantInfo", CommonMerchantInfoService.class);
    }

    @Bean
    public JsonProxyFactoryBean systemService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(contractActivityUrl + "/rpc/system", SystemService.class);
    }

    @Bean
    public JsonProxyFactoryBean activitySystemService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(contractActivityUrl + "/rpc/system", ActivitySystemService.class);
    }


    @Bean
    public JsonProxyFactoryBean spaService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(contractActivityUrl + "/rpc/spa", SpaService.class);
    }

    @Bean
    public JsonProxyFactoryBean CommonAppInfoService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(merchantBusinessOpenUrl + "/rpc/common/appInfo", CommonAppInfoService.class);
    }


    @Bean
    public JsonProxyFactoryBean icbcService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingMerchantContractBean(merchantContractUrl + "/rpc/icbc_service", IcbcService.class);
    }

    @Bean
    public JsonProxyFactoryBean jsbService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingMerchantContractBean(merchantContractUrl + "/rpc/jsb", JsbService.class);
    }

    @Bean
    public JsonProxyFactoryBean pabService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingMerchantContractBean(merchantContractUrl + "/rpc/pab", PabService.class);
    }


    @Bean
    public JsonProxyFactoryBean pingAnService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingMerchantContractBean(merchantContractUrl + "/rpc/pingan", PingAnService.class);
    }

    @Bean
    public JsonProxyFactoryBean paywayActivityService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(contractActivityUrl + "/rpc/payway_activity", PaywayActivityService.class);
    }

    @Bean
    public JsonProxyFactoryBean bankFeeRateService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(tradeManageUrl + "/rpc/bankFeeRate", BankFeeRateService.class);
    }

    @Bean
    public JsonProxyFactoryBean campusZoneService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(campusCenterUrl + "/rpc/campusZone", CampusZoneService.class);
    }

    @Bean
    public JsonProxyFactoryBean ZftService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingMerchantContractBean(merchantContractUrl + "/rpc/zft", ZftService.class);
    }


    @Bean
    public JsonProxyFactoryBean FitnessService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingMerchantContractBean(merchantContractUrl + "/rpc/fitness", FitnessService.class);
    }

    @Bean
    public JsonProxyFactoryBean FitnessNotifyService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(creditPayBackendUrl + "/rpc/fitnessNotify", FitnessNotifyService.class);
    }

    @Bean
    public JsonProxyFactoryBean FqConfigService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(creditPayBackendUrl + "/rpc/fqConfig", FqConfigService.class);
    }

    @Bean
    public JsonProxyFactoryBean ClearanceService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(withdrawServiceUrl + "/rpc/clearanceProvider", ClearanceService.class);
    }

    @Bean
    public JsonProxyFactoryBean TodoDetailService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(crmTodoUrl + "/rpc/crm/todoDetail", TodoDetailService.class);
    }

    @Bean
    public JsonProxyFactoryBean sceneConfigService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(tradeManageUrl + "/rpc/sceneConfig", SceneConfigService.class);
    }

    @Bean
    public JsonProxyFactoryBean iRiskBlistSceneService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(riskServiceUrl + "/rpc/blistScene", IRiskBlistSceneService.class);
    }

    @Bean
    public JsonProxyFactoryBean PayBusinessOpenApplyManageService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(payBusinessOpenUrl + "/rpc/apply/manage", PayBusinessOpenApplyManageService.class);
    }

    @Bean
    public JsonProxyFactoryBean brandFacade() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(brandBusinessUrl + "/rpc/brand", BrandFacade.class);
    }

    @Bean
    public JsonProxyFactoryBean photoInfoService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(merchantCenterUrl + "/rpc/photoinfo", com.wosai.mc.service.PhotoInfoService.class);
    }

    @Bean
    public JsonProxyFactoryBean simpleSmartPosInfoService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(dcServiceUrl + "/rpc/simpleSmartPos", SimpleSmartPosInfoService.class);
    }

    @Bean
    public JsonProxyFactoryBean terminalManagementService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(merchantContractAccessUrl + "/rpc/terminal-management", TerminalManagementService.class);
    }

    @Bean
    public JsonProxyFactoryBean timeTaskService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(merchantBusinessOpenUrl + "/rpc/timeTask", TimeTaskService.class);
    }

    @Bean
    public JsonProxyFactoryBean commonFieldService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(merchantBusinessOpenUrl + "/rpc/common/field", CommonFieldService.class);
    }

    @Bean
    public JsonProxyFactoryBean crmCommonFieldService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(merchantBusinessOpenUrl + "/rpc/common/field", CrmCommonFieldService.class);
    }

    @Bean
    public JsonProxyFactoryBean bankAccountService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(bankServiceUrl + "/rpc/bankAccount", BankAccountService.class);
    }

    @Bean
    public JsonProxyFactoryBean accountBankAccountService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(bankServiceUrl + "/rpc/bankAccount", AccountBankAccountService.class);
    }

    @Bean
    public JsonProxyFactoryBean bankLicenseUpdateChangeCardService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(bankServiceUrl + "/rpc/license/change-card", BankLicenseUpdateChangeCardService.class);
    }

    @Bean
    public JsonProxyFactoryBean lzService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingMerchantContractBean(merchantContractUrl + "/rpc/luzhou", LuZhouService.class);
    }
    @Bean
    public JsonProxyFactoryBean umbService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingMerchantContractBean(merchantContractUrl + "/rpc/umb", UmbService.class);
    }


    @Bean(name = "iotBusinessOpenService")
    public JsonProxyFactoryBean businessOpenService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(jupiterUrl + "/rpc/businessOpen", BusinessOpenService.class);
    }

    @Bean
    public JsonProxyFactoryBean cmbcService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingMerchantContractBean(merchantContractUrl + "/rpc/cmbc_bank", CMBCService.class);
    }


    @Bean
    public JsonProxyFactoryBean psbcsxService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingMerchantContractBean(merchantContractUrl + "/rpc/psbcsx", PsbcsxService.class);
    }

    @Bean
    public JsonProxyFactoryBean qldMerchantInfoService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean( partnerPaymentHubUrl+ "/rpc/qld_merchant_info", QldMerchantInfoService.class);
    }


}
