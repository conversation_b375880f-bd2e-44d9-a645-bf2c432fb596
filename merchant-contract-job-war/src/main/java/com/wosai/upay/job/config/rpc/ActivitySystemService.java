package com.wosai.upay.job.config.rpc;

import com.wosai.model.SystemResponse;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2025/7/8 16:43
 */
public interface ActivitySystemService {

    /**
     * 小微商户升级入网成功,由merchant-contract-job项目主动调用,基于传入的商户上下文,另外还把对应的子商户号传进来
     *
     * @param merchantSn   商户号
     * @param operatorId   客户经理的ID
     * @param contextParam 进件上下文
     * @param subMchId
     * @return 提交结果
     */
    SystemResponse submitWechatAuthForNewMerchant(@NotBlank(message = "商户号不能为空") String merchantSn,
                                                  @NotBlank(message = "操作id不能为空") String operatorId,
                                                  @NotNull(message = "商户上下文") Map<String, Object> contextParam,
                                                  @NotBlank(message = "支付源子商户号") String subMchId);


    /**
     * 小微商户升级入网成功,由merchant-contract-job项目主动调用,基于传入的商户上下文,另外还把对应的子商户号传进来
     *
     * @param merchantSn   商户号
     * @param operatorId   客户经理的ID
     * @param contextParam 进件上下文
     * @param subMchId
     * @return 提交结果
     */
    SystemResponse submitAlipayAuthForNewMerchant(@NotBlank(message = "商户号不能为空") String merchantSn,
                                                  @NotBlank(message = "操作id不能为空") String operatorId,
                                                  @NotNull(message = "商户上下文")  Map<String, Object> contextParam,
                                                  @NotBlank(message = "支付源子商户号") String subMchId);

}
