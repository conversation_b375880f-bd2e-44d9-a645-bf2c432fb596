package com.wosai.upay.job.handlers;

import java.util.*;

import cn.hutool.core.util.StrUtil;
import com.shouqianba.cua.enums.contract.ContractTaskTypeEnum;
import com.wosai.upay.job.constant.ContractRuleConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Maps;
import com.shouqianba.cua.enums.contract.ContractSubTaskProcessStatusEnum;
import com.shouqianba.cua.enums.contract.ContractSubTaskTypeEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.exception.CoreMerchantNotExistsException;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.BlackListBiz;
import com.wosai.upay.job.biz.ContractTaskBiz;
import com.wosai.upay.job.biz.JdBiz;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.constant.CommonConstants;
import com.wosai.upay.job.constant.SCAlarmCopywritingConstants;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.*;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.MultiProviderContractEvent;
import com.wosai.upay.job.model.alarm.SettlementCardAlarmDto;
import com.wosai.upay.job.monitor.MonitorLog;
import com.wosai.upay.job.providers.BasicProvider;
import com.wosai.upay.job.providers.DefaultChangeTradeParamsBiz;
import com.wosai.upay.job.refactor.dao.McAcquirerDAO;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.service.TaskResultService;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.merchant.contract.model.ContractResponse;

/**
 * 报备 sub_task 处理
 *
 * @Author: jerry
 * @date: 2019/4/2 18:16
 * @Description:报备任务处理
 */
@Component
@Order(99)
public class ContractSubTaskHandler extends AbstractSubTaskHandler {
    //报备系统异常
    public static final int RESULT_CODE_SYSTEM_EXCEPTION = 500;
    //报备成功
    public static final int RESULT_CODE_SUCCESS = 200;
    public static final int RESULT_CODE_WECHAT_NO_UPDATE = 999;

    @Autowired
    private ContractTaskMapper contractTaskMapper;

    @Autowired
    private ContractStatusMapper contractStatusMapper;

    @Autowired
    private MultiProviderContractEventMapper multiEventMapper;

    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;

    @Autowired
    private PayWayConfigChangeMapper payWayConfigChangeMapper;

    @Autowired
    TaskResultService taskResultService;

    @Autowired
    private ChatBotUtil chatBotUtil;

    @Autowired
    private MonitorLog monitorLog;
    @Autowired
    private RuleContext ruleContext;

    @Autowired
    private BlackListBiz blackListBiz;

    private final static Logger log = LoggerFactory.getLogger(ContractSubTaskHandler.class);
    public static final String AUTHORIZE_STATE_AUTHORIZED = "AUTHORIZE_STATE_AUTHORIZED";

    @Autowired
    private ContractTaskBiz contractTaskBiz;

    @Autowired
    private DefaultChangeTradeParamsBiz defaultChangeTradeParamsBiz;
    @Autowired
    private JdBiz jdBiz;
    
    @Autowired
    private  MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;
    
    @Autowired
    private McAcquirerDAO mcAcquirerDAO;
    @Autowired
    private MerchantProviderParamsDAO merchantProviderParamsDAO;

    @Override
    public boolean supports(ContractTask task, ContractSubTask subTask) {
        //小微升级的入网任务只允许 MicroUpgradeContractSubTaskHandler 类处理
        if(ContractTaskTypeEnum.NEW_MERCHANT_ONLINE.getValue().equals(task.getType())
                && StrUtil.contains(task.getRule_group_id(), ContractRuleConstants.RULE_GROUP_MICROUPGRADE)) {
            return Boolean.FALSE;
        }
        return true;
    }

    @Override
    protected void handleError(ContractTask task, ContractSubTask subTask, Exception e) throws Exception {
        if (e instanceof CoreMerchantNotExistsException) {
            ContractTask updateValue = new ContractTask()
                    .setId(task.getId())
                    .setStatus(6)
                    .setResult(null);
            contractTaskBiz.update(updateValue);
        }
        throw e;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doHandle(ContractTask task, ContractSubTask subTask) {
        long start = System.currentTimeMillis();
        ContractResponse contractResponse = null;
        try {
            String merchantSn = subTask.getMerchant_sn();
            String channelName = subTask.getChannel();
            BasicProvider provider = providerFactory.getProviderByName(channelName);
            if (StringUtils.isEmpty(subTask.getContract_rule())) {
                log.info("subTask do not have contract rule : {}", subTask);
                chatBotUtil.sendMessageToContractWarnChatBot("subTask do not have contract rule, sub_task_id: " + subTask.getId());
                return;
            } else {
                contractResponse = processRuleTask(provider, task, subTask);
            }
            if (contractResponse == null) {
                log.info("merchantSn {} subTask {} channelName {} processTask return null", merchantSn, subTask.getId(), channelName);
                return;
            }
            int code = contractResponse.getCode();
            Map response = Maps.newHashMap();
            //sp要求response格式
            response.put("responseParam", contractResponse.getResponseParam());
            response.put("tradeParam", contractResponse.getTradeParam());
            if (RESULT_CODE_SUCCESS == code) {
                successHandle(task, subTask, response, contractResponse);
            } else {
                failHandler(task, subTask, response, contractResponse, RESULT_CODE_SYSTEM_EXCEPTION == code);
            }
        } finally {
            if (contractResponse == null) {
                contractResponse = new ContractResponse();
                contractResponse.setCode(500);
            }
            monitorLog.recordMonitor(subTask, contractResponse, System.currentTimeMillis() - start);
        }
    }


    public ContractResponse processRuleTask(BasicProvider provider, ContractTask contractTask, ContractSubTask contractSubTask) {
        Assert.notNull(provider, "根据任务规则获取provider为空");
        return provider.processTaskByRule(contractTask, ruleContext.getContractRule(contractSubTask.getContract_rule()).getContractChannel(), contractSubTask);
    }


    /**
     * 子任务失败处理逻辑
     * 1.更新子任务结果状态和请求响应结果
     * 2.影响父任务的子任务 更新父任务状态和信息 (业务异常 状态为失败 系统异常 状态为成功)
     */
    private void failHandler(ContractTask contractTask, ContractSubTask contractSubTask, Map response, ContractResponse contractResponse, boolean isSystemFail) {
        // 特殊处理
        if (!StringUtils.isEmpty(contractResponse.getMessage()) && (contractResponse.getMessage().contains("相同客户名不允许进件")
                || contractResponse.getMessage().contains("商户名重复")
                || contractResponse.getMessage().contains("该商户名称已存在"))) {
            handleSameName(contractTask);
            return;
        }

        Long taskId = contractTask.getId();
        String channelName = contractSubTask.getChannel();
        String merchantSn = contractSubTask.getMerchant_sn();
        Map result = CollectionUtil.hashMap("channel", channelName,
                "message", contractResponse.getMessage(),
                "code", contractResponse.getCode(),
                "result", contractResponse.getMessage());
        if (contractSubTask.getPayway() != null) {
            result.put("payway", contractSubTask.getPayway());
        }
        String resultMessage = JSON.toJSONString(result);
        ContractSubTask update = new ContractSubTask()
                .setId(contractSubTask.getId())
                .setRequest_body(JSON.toJSONString(contractResponse.getRequestParam()))
                .setResponse_body(JSON.toJSONString(response))
                .setResult(resultMessage);
        int resultStatus = 6;
        if (isSystemFail) {
            resultStatus = 1;
        }
        update.setStatus(resultStatus);
        contractSubTaskMapper.updateByPrimaryKey(update);
        //子任务 影响总状态时 更新父任务的状态
        if (1 == contractSubTask.getStatus_influ_p_task()) {
            if (resultStatus == 1) {
//                contractTaskMapper.updateByPrimaryKey(new ContractTask().setId(taskId).setResult(resultMessage));
                // TODO 更新contract_task表status,并根据type和status判断是否发送消息到神策
                contractTaskBiz.update(new ContractTask().setId(taskId).setResult(resultMessage));
            } else {
                taskResultService.changeStatusAndResultV2(taskId, contractSubTask.getId(), resultStatus, resultMessage, false);
            }

        } else {
            // 如果有子任务依赖于 这条子任务，并且该子任务是影响主任务状态的 需要将总任务置为失败
            if (resultStatus == 6) {
                List<ContractSubTask> contractSubTasks = contractSubTaskMapper.selectByDependTaskId(contractSubTask.getId());
                if (WosaiCollectionUtils.isNotEmpty(contractSubTasks) && contractSubTasks.stream().anyMatch(r -> r.getStatus_influ_p_task() == 1)) {
                    taskResultService.changeStatusAndResultV2(taskId, contractSubTask.getId(), resultStatus, resultMessage, false);
                }
            }
        }

        try {
            // 结算卡开户行清算行号不支持报错推送
            settlementCardNonsupportAlarm(contractTask,contractSubTask, contractResponse);
        } catch (Throwable throwable) {
            // 出现异常不影响主流程
            log.error("settlementCardNonsupportAlarm", throwable);
        }
        //业务异常发钉钉
        chatBotUtil.sendMessageToContractWarnChatBot("contract  fail  merchant: " + merchantSn + " channelName: " + channelName + " payWay: "
                + contractSubTask.getPayway() + " code: " + contractResponse.getCode() + " message: " + contractResponse.getMessage());
        monitorLog.recordMonitor("进件失败", "merchant: " + merchantSn + " channelName: " + channelName + " payWay: "
                + contractSubTask.getPayway() + " code: " + contractResponse.getCode() + " message: " + contractResponse.getMessage());
        blackListBiz.syncBlacklist(contractSubTask, merchantSn, contractSubTask.getPayway(), contractResponse.getMessage());
        //系统异常重试
        if (isSystemFail) {
            handleRetry(contractSubTask, contractTask);
        }
        //京东钱包开通失败处理
        if(Objects.equals(contractSubTask.getPayway(), PaywayEnum.JD_WALLET.getValue()) && Objects.equals(contractSubTask.getTask_type(), ContractSubTaskTypeEnum.MERCHANT_OPENING.getValue())) {
            //由于contract_status记录的是最终使用的通道,所以对于多通道同时开通京东白条的时候,只有当前再用通道失败才会通知到支付,这样能避免非当前再用通道失败导致通知错误
            final boolean consistent = Optional.ofNullable(contractStatusMapper.selectByMerchantSn(merchantSn))
                    .filter(contractStatus -> Objects.equals(contractStatus.getAcquirer(),
                            defaultChangeTradeParamsBiz.getAcquireByContractRule(contractSubTask.getContract_rule())))
                    .isPresent();

            if(consistent) {
                final String failMessage = StringUtils.isEmpty(contractResponse.getMessage()) ? "开通失败" : contractResponse.getMessage();
                jdBiz.openFail(merchantSn,failMessage);
            }
        }

    }

    /**
     * 结算卡开户行清算行号不支持报错推送
     *
     * @param contractTask
     * @param contractSubTask  contractSubTask
     * @param contractResponse 开户返回体
     */
    public void settlementCardNonsupportAlarm(ContractTask contractTask, ContractSubTask contractSubTask,
        ContractResponse contractResponse) {
        // apollo配置控制，默认为false，不进行相关操作
        if (!applicationApolloConfig.getSettlementCardAlarm()) {
            log.warn("[ContractSubTaskHandler] <settlementCardNonsupportAlarm> apollo config is false, skip alarm");
            return;
        }
        String message = contractResponse.getMessage();
        if (StringUtils.isEmpty(message)) {
            log.warn("[ContractSubTaskHandler] <settlementCardNonsupportAlarm> contractResponse message is empty");
            return;
        }
        if (!message.contains(SCAlarmCopywritingConstants.FY_ALARM_COPYWRITING)
            && !message.contains(SCAlarmCopywritingConstants.HK_ALARM_COPYWRITING)
            && !message.contains(SCAlarmCopywritingConstants.LKL_ALARM_COPYWRITING_ERROR)) {
            log.info(
                "[ContractSubTaskHandler] <settlementCardNonsupportAlarm> contractResponse message is not match, message: {}",
                message);
            return;
        }
        log.info(
            "[ContractSubTaskHandler] <settlementCardNonsupportAlarm> contractResponse message: {},contractTask no is {},contractSubTask is {}",
            message, contractTask.getId(), contractSubTask.getId());
        String eventContext = contractTask.getEvent_context();
        String clearingNumber = "";
        String openingNumber = "";
        String bankName = "";
        String branchName = "";
        if (org.apache.commons.lang3.StringUtils.isNotBlank(eventContext)) {
            Map<String, Object> contextMap =
                JSONObject.parseObject(eventContext, new TypeReference<Map<String, Object>>() {});
            Map<String, Object> bankAccount =
                (Map<String, Object>)contextMap.getOrDefault("bankAccount", new HashMap<>());
            if (bankAccount.containsKey("clearing_number")) {
                clearingNumber = BeanUtil.getPropString(bankAccount, "clearing_number");
            }
            if (bankAccount.containsKey("opening_number")) {
                openingNumber = BeanUtil.getPropString(bankAccount, "opening_number");
            }
            if (bankAccount.containsKey("bank_name")) {
                bankName = BeanUtil.getPropString(bankAccount, "bank_name");
            }
            if (bankAccount.containsKey("branch_name")) {
                branchName = BeanUtil.getPropString(bankAccount, "branch_name");
            }
        }
        ContractRule contractRule = ruleContext.getContractRule(contractSubTask.getContract_rule());
        String provider = contractRule.getProvider();
        int providerNo = 0;
        if (org.apache.commons.lang3.StringUtils.isNotBlank(provider)) {
            providerNo = Integer.parseInt(provider);
        }
        String acquirerName = mcAcquirerDAO.getAcquirerName(contractRule.getAcquirer());
        String merchantSn = contractSubTask.getMerchant_sn();
        // 发送钉钉告警
        SettlementCardAlarmDto settlementCardAlarmDto = new SettlementCardAlarmDto();
        settlementCardAlarmDto.setMerchantSn(merchantSn);
        Optional<MerchantProviderParamsDO> paramsDoOptional = merchantProviderParamsDAO
            .getBySnAndProviderAndPayWay(merchantSn, providerNo, PaywayEnum.ACQUIRER.getValue());
        paramsDoOptional
            .ifPresent(paramsDO -> settlementCardAlarmDto.setAcquiringMerchantNo(paramsDO.getPayMerchantId()));
        settlementCardAlarmDto.setAcquiringInstitutionName(acquirerName);
        settlementCardAlarmDto.setErrorRemarks(message);
        settlementCardAlarmDto.setBranchName(branchName);
        settlementCardAlarmDto.setOpeningNumber(openingNumber);
        settlementCardAlarmDto.setBankName(bankName);
        settlementCardAlarmDto.setClearingNumber(clearingNumber);
        settlementCardAlarmDto.setOperationProcess(SCAlarmCopywritingConstants.ALARM_OPERATION_PROCESS);
        sendAlarmMessage(settlementCardAlarmDto);
    }

    private void sendAlarmMessage(SettlementCardAlarmDto settlementCardAlarmDto) {
        if (Objects.isNull(settlementCardAlarmDto)) {
            log.warn("[ContractSubTaskHandler] <sendAlarmMessage> settlementCardAlarmDto is null");
            return;
        }
        String alarmMessage =
            String.format("收钱吧商户号: %s\n收单机构商户号: %s\n收单机构: %s\n清算行号：%s\n开户行号：%s\n银行名：%s\n开户行名：%s\n报错备注: %s\n操作流程: %s",
                settlementCardAlarmDto.getMerchantSn(), settlementCardAlarmDto.getAcquiringMerchantNo(),
                settlementCardAlarmDto.getAcquiringInstitutionName(), settlementCardAlarmDto.getClearingNumber(),
                settlementCardAlarmDto.getOpeningNumber(), settlementCardAlarmDto.getBankName(),
                settlementCardAlarmDto.getBranchName(), settlementCardAlarmDto.getErrorRemarks(),
                settlementCardAlarmDto.getOperationProcess());
        try {
            chatBotUtil.sendMessageToSettlementCardAlarmChatBot(alarmMessage);
        } catch (Exception e) {
            log.error(
                "[ContractSubTaskHandler] <sendAlarmMessage> send settlement card alarm message error, settlementCardAlarmDto: {}",
                settlementCardAlarmDto, e);
        }
    }

    private void handleSameName(ContractTask contractTask) {
        Map contextParam = JSON.parseObject(contractTask.getEvent_context(), Map.class);
        String merchantName = BeanUtil.getPropString(contextParam, "merchant.name");
        BeanUtil.setNestedProperty(contextParam, "merchant.name", nextMerchantName(merchantName));
        ContractTask updateValue = new ContractTask()
                .setId(contractTask.getId())
                .setEvent_context(JSON.toJSONString(contextParam));
        contractTaskMapper.updateByPrimaryKey(updateValue);
    }

    private String nextMerchantName(String merchantName) {
        String digit = "";
        for (int i = merchantName.length() - 1; i >= 0; i--) {
            if (!Character.isDigit(merchantName.charAt(i))) {
                digit = merchantName.substring(i + 1);
                merchantName = merchantName.substring(0, i + 1);
                break;
            }
        }
        if (digit.isEmpty()) {
            return merchantName + "1";
        } else {
            return merchantName + (Long.valueOf(digit) + 1);
        }
    }

    private void handleRetry(ContractSubTask sub, ContractTask task) {
        String rule = sub.getContract_rule();
        Integer retried = sub.getRetry();
        if (StringUtils.isEmpty(rule)) {
            return;
        }
        ContractRule contractRule = ruleContext.getContractRule(rule);
        retried++;
        contractSubTaskMapper.updateByPrimaryKey(new ContractSubTask().setId(sub.getId()).setRetry(retried));
        if (retried >= contractRule.getRetry()) {
            contractTaskMapper.updatePriority(CommonConstants.SYSTEM_ERROR_PRIORITY, task.getId());
        }
    }


    /**
     * 子任务处理成功逻辑
     * 返回tradeParam包含contractId 表示此任务成功了需要等待回调
     * 如果需要等待回调 更新contractId
     * 不需要等待回调 子任务成功
     * 1.下游任务标记为可调度
     * 2.更新总任务
     * 3 判断是否生成切通道
     */
    private void successHandle(ContractTask contractTask, ContractSubTask contractSubTask, Map response, ContractResponse contractResponse) {
        String merchantSn = contractSubTask.getMerchant_sn();
        String contractId = (String) contractResponse.getTradeParam().get("contractId");
        String channelName = contractSubTask.getChannel();
        Long subTaskId = contractSubTask.getId();
        Long taskId = contractTask.getId();
        String merchantProviderParamsId = contractResponse.getMerchantProviderParamsId();
        if (!StringUtils.isEmpty(merchantProviderParamsId)) {
            response.put("merchantProviderParamsId", merchantProviderParamsId);
        }
        ContractSubTask update = new ContractSubTask()
                .setId(subTaskId)
                .setRequest_body(JSON.toJSONString(contractResponse.getRequestParam()))
                .setResponse_body(JSON.toJSONString(response))
                .setResult(contractResponse.getMessage())
                .setPriority(new Date());

        boolean isPending = !StringUtil.empty(contractId);
        int resultStatus = TaskStatus.PROGRESSING.getVal();
        if (isPending) {
            update.setContract_id(contractId);
        } else {
            resultStatus = TaskStatus.SUCCESS.getVal();
        }
        //更新子任务业务值
        contractSubTaskMapper.updateByPrimaryKey(update);
        //更新子任务状态 若更新成功时 进行父任务状态更新
        int changeRow = contractSubTaskMapper.setSubTaskStatus(TaskStatus.PENDING.getVal(), resultStatus, subTaskId);
        int changeRow2 = contractSubTaskMapper.setSubTaskStatus(TaskStatus.PROGRESSING.getVal(), resultStatus, subTaskId);
        int changeRow3 = contractSubTaskMapper.setSubTaskStatus(ContractSubTaskProcessStatusEnum.WAIT_CALL_BACK.getValue(), resultStatus, subTaskId);
        if (isPending || (changeRow + changeRow2 + changeRow3) < 1) {
            return;
        }
        //把这个子任务 下游任务标记为可调度
        contractSubTaskMapper.setEnableScheduleByDepId(subTaskId);
        //更新父任务 affect_status_success_task_count 如果全成功了 更新status
        if (contractSubTask.getStatus_influ_p_task() == 1) {
            addAffectStatusSuccessTaskCountBiz.addAffectStatusSuccessTaskCount(taskId);
            ContractTask task = contractTaskMapper.selectByPrimaryKey(taskId);
            if (!task.getStatus().equals(TaskStatus.PROGRESSING.getVal()) && !task.getStatus().equals(TaskStatus.FAIL.getVal())) {
//                taskResultService.changeStatusAndResult(taskId, task.getStatus(), null, false);
                taskResultService.changeStatusAndResultV2(taskId, subTaskId, task.getStatus(), null, false);
            }
        }
        //实名升级任务等待授权后再切
        if (ProviderUtil.CONTRACT_TYPE_AUTH.equals(contractTask.getType())) {
            return;
        }
        // 如果存在多通道入网事件，并且是不影响主任务的任务，并且该任务的通道和该商户的contract_status的收单机构一致,就去切
        if (ProviderUtil.SUB_TASK_TASK_TYPE_CONTRACT.equals(contractSubTask.getTask_type())) {
            MultiProviderContractEvent event = multiEventMapper.selectMultiEventByMerchantSnAndTaskId(contractTask.getMerchant_sn(), contractTask.getId());
            // 如果影响主任务直接return
            if (contractTask.getId() != null && event != null) {
                if (contractSubTask.getStatus_influ_p_task() == 0) {
                    ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(contractTask.getMerchant_sn());
                    // 如果是不影响主任务的走到这里，contract_status一定是成功的了,并且该任务是默认通道，生成该任务不影响主通道任务的子任务的切换通道任务

                    if (contractStatus.getStatus() == ContractStatus.STATUS_SUCCESS && ProviderUtil.switchRuleGroupIdToAcquirer(contractTask.getRule_group_id()).equals(contractStatus.getAcquirer())) {
                        insertPayWayConfigChangeTask(channelName, merchantSn, response, contractSubTask);
                    }
                }
                return;
            }
        }
        insertPayWayConfigChangeTask(channelName, merchantSn, response, contractSubTask);
    }

    private void insertPayWayConfigChangeTask(String channelName, String merchantSn, Map response, ContractSubTask contractSubTask) {
        boolean changeConfig = ProviderUtil.SUB_TASK_TASK_TYPE_CONTRACT.equals(contractSubTask.getTask_type());
        //报备 需要切通道的subTask成功后 需要生成切通道任务
        if (changeConfig && 1 == contractSubTask.getChange_config()) {
            Map tradeParam = (Map) response.get("tradeParam");
            if (CollectionUtils.isEmpty(tradeParam)) {
                return;
            }
            PayWayConfigChange payWayConfigChange = new PayWayConfigChange().
                    setBody(JSON.toJSONString(response))
                    .setMerchant_sn(merchantSn)
                    .setPayway(contractSubTask.getPayway())
                    .setChannel(channelName);
            payWayConfigChangeMapper.insertSelective(payWayConfigChange);
        }
    }

}
