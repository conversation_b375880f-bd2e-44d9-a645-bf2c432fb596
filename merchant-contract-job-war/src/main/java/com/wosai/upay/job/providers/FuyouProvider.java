package com.wosai.upay.job.providers;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ContractSubTaskProcessStatusEnum;
import com.shouqianba.cua.enums.contract.ContractSubTaskTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.BankAccountTypeEnum;
import com.shouqianba.cua.enums.core.BusinessLicenseTypeEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.model.enums.TradeParamsDisableReasonAccessSideEnum;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.databus.event.terminal.basic.TerminalBasicInsertEvent;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.model.StoreInfo;
import com.wosai.mc.model.req.UpdateMerchantReq;
import com.wosai.upay.bank.model.MerchantBusinessLicense;
import com.wosai.upay.bank.service.BankBusinessLicenseService;
import com.wosai.upay.core.bean.model.TradeExtConfigContentModel;
import com.wosai.upay.core.bean.request.TradeExtConfigCreateRequest;
import com.wosai.upay.core.bean.request.TradeExtConfigQueryRequest;
import com.wosai.upay.core.bean.request.TradeExtConfigUpdateRequest;
import com.wosai.upay.core.bean.response.TradeExtConfigQueryResponse;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.core.model.MerchantBusinessLicence;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.*;
import com.wosai.upay.job.biz.acquirePos.T9HandleFactory;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.config.ApolloConfig;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.job.enume.ScheduleEnum;
import com.wosai.upay.job.exception.ContextParamException;
import com.wosai.upay.job.helper.MerchantNameHelper;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.mapper.ProviderTerminalMapper;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.model.dto.ProviderTerminalContext;
import com.wosai.upay.job.refactor.biz.acquirer.fuyou.FuYouAcquirerFacade;
import com.wosai.upay.job.refactor.biz.params.MerchantTradeParamsBiz;
import com.wosai.upay.job.refactor.dao.ContractSubTaskDAO;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.model.converter.MerchantProviderParamsConverter;
import com.wosai.upay.job.refactor.model.entity.ContractSubTaskDO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsExtDO;
import com.wosai.upay.job.refactor.task.license.BusinessLicenceCertificationTask;
import com.wosai.upay.job.refactor.task.license.update.LicenseUpdateToAcquirerTaskHandler;
import com.wosai.upay.job.refactor.task.rotational.FuYouBusinessLicenseManualAuditSubTaskProcessor;
import com.wosai.upay.job.refactor.task.rotational.RotationalTask;
import com.wosai.upay.job.refactor.task.rotational.entity.RotationalSubTaskTypeEnum;
import com.wosai.upay.job.refactor.task.rotational.entity.RotationalTaskContext;
import com.wosai.upay.job.service.MerchantProviderParamsService;
import com.wosai.upay.job.util.*;
import com.wosai.upay.merchant.contract.constant.*;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.MerchantProviderTradeParams;
import com.wosai.upay.merchant.contract.model.WeixinConfig;
import com.wosai.upay.merchant.contract.model.fuyou.SignElectronicAgreementRequest;
import com.wosai.upay.merchant.contract.model.fuyou.TerminalOperateRequest;
import com.wosai.upay.merchant.contract.model.fuyou.response.MchntOpenUpayQrResponse;
import com.wosai.upay.merchant.contract.model.fuyou.response.MchntQueryTZResponse;
import com.wosai.upay.merchant.contract.model.provider.FuyouParam;
import com.wosai.upay.merchant.contract.model.terminal.AddTermInfoDTO;
import com.wosai.upay.merchant.contract.service.FuyouService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static com.wosai.upay.job.constant.ContractRuleConstants.CHANGE_TO_FUYOU_RULE_GROUP;

/**
 * @Description: Haike处理类
 * <AUTHOR>
 * @Date 2023/7/10
 */
@Component(ProviderUtil.FUYOU_CHANNEL)
@Slf4j
@Transactional
public class FuyouProvider extends AbstractProvider {


    private static final String RET_CODE = "ret_code";

    /**
     * 商户未参加行业活动
     */
    private static final String MERCHANT_DID_NOT_ATTEND_INDUSTRY_EVENTS_ERRCODE = "0001";
    @Autowired
    private FuyouService fuyouService;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private com.wosai.mc.service.MerchantService centerMerchantService;

    @Autowired
    private RuleContext ruleContext;

    @Autowired
    private BankBusinessLicenseService bankBusinessLicenseService;

    @Autowired
    private WechatAuthBiz wechatAuthBiz;

    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    @Autowired
    private ContractStatusMapper contractStatusMapper;

    @Autowired
    private WxSettlementIdChangeBiz wxSettlementIdChangeBiz;

    @Autowired
    private ContractTaskBiz contractTaskBiz;

    @Autowired
    private ProviderTerminalMapper terminalMapper;

    private static String alipayChannelNo = "****************";

    private static String wechatChannelNo = "*********";

    // 对公凭证
    private static String CERTFICATE = "fuyou_certificate";

    private static String UNION_CHANNEL_NO = "fuyou";

    public static final String MODIFY_TP_YL = "YL";

    // 实时生效
    public static final String EFFECTIVE_ACNT = "1";

    //审核完成
    public static final String COMPLETED = "04";

    //审核拒绝
    public static final String REJECTED = "00";

    @Value("${indirect-pay.dev_code}")
    private String devCode;

    // 对公银行卡信息
    private static String FUYOU_PUBLIC_BANK_ACCOUNT = "fuyou_public_bank_account";

    @Autowired
    private T9HandleFactory factory;

    @Autowired
    private MerchantProviderParamsDAO merchantProviderParamsDAO;

    @Autowired
    private IndustryMappingCommonBiz industryMappingCommonBiz;

    @Autowired
    private ChatBotUtil chatBotUtil;

    @Autowired
    private SubBizParamsBiz subBizParamsBiz;

    @Autowired
    private MerchantTradeParamsBiz merchantTradeParamsBiz;

    @Autowired
    private MerchantProviderParamsService merchantProviderParamsService;
    @Autowired
    @Qualifier("fuyouUnionPayThreadPoolTaskExecutor")
    private ThreadPoolTaskExecutor fuyouUnionPayThreadPoolTaskExecutor;

    @Resource
    private RotationalTask rotationalTask;

    @Resource
    private FuYouAcquirerFacade fuYouAcquirerFacade;

    @Resource
    MerchantNameHelper merchantNameHelper;

    @Override
    public Map doWeixinSubDevConfig(WeixinConfig weixinConfig, MerchantProviderParams param) {
        FuyouParam fuyouParam = contractParamsBiz.buildContractParams("fuyou", FuyouParam.class);
        return fuyouService.weixinSubdevConfig(weixinConfig, fuyouParam);
    }


    /**
     * 处理增网增终新增任务
     *
     * @param event
     * @return
     */
    @Override
    public void produceInsertTerminalTaskByRule(TerminalBasicInsertEvent event, MerchantInfo merchant) {
        String merchantSn = merchant.getSn();
        ContractTask task = new ContractTask().setMerchant_sn(merchantSn)
                .setMerchant_name(merchant.getName())
                .setStatus(0)
                .setRule_group_id(McConstant.RULE_GROUP_FUYOU)
                .setType(ProviderUtil.CONTRACT_TYPE_ADDTERM)
                .setAffect_status_success_task_count(0)
                .setAffect_sub_task_count(1);
        if (event != null) {
            task.setEvent_context(JSONObject.toJSONString(event));
        }
        contractTaskBiz.insert(task);
        ContractSubTask subTask = new ContractSubTask()
                .setTask_type(ProviderUtil.SUB_TASK_TASK_TYPE_ADD_TERM)
                .setStatus_influ_p_task(1)
                .setChannel(ProviderUtil.FUYOU_CHANNEL)
                .setMerchant_sn(merchant.getSn())
                .setContract_rule(McConstant.RULE_GROUP_FUYOU)
                .setRule_group_id(McConstant.RULE_GROUP_FUYOU)
                .setSchedule_dep_task_id(0L)
                .setSchedule_status(1)
                .setP_task_id(task.getId());
        contractSubTaskMapper.insert(subTask);
    }


    @Override
    protected ContractSubTask produceUpdateTaskByRule(String merchantSn, ContractEvent event, Map<String, Object> paramContext, ContractRule contractRule) {
        boolean isAcquirerMerchantDisabledByMicroUpdate = merchantTradeParamsBiz.listMerchantAcquirerParamDisableReasons(merchantSn, AcquirerTypeEnum.FU_YOU.getValue())
                .stream()
                .anyMatch(disableReasonRspDTO ->
                        Objects.equals(TradeParamsDisableReasonAccessSideEnum.CUA, disableReasonRspDTO.getAccessSide()) &&
                                StringUtils.equals(disableReasonRspDTO.getDisableReason(),
                                        BusinessLicenceCertificationTask.MICRO_UPGRADE_ACQUIRER_MERCHANT_DISABLE_REASON));
        if (isAcquirerMerchantDisabledByMicroUpdate) {
            log.info("收单机构商户号禁用原因是小微升级,不生成更新任务, merchantSn:{}, acquirer:fuyou", merchantSn);
            return null;
        }
        String acquirer = acquirerBiz.getMerchantAcquirer(merchantSn);
        int influPtask = AcquirerTypeEnum.FU_YOU.getValue().equals(acquirer) || isSubBiz(merchantSn, AcquirerTypeEnum.FU_YOU.getValue()) ? contractRule.getUpdateInfluPtask() : 0;

        ContractSubTask subTask = new ContractSubTask()
                .setMerchant_sn(merchantSn)
                .setStatus_influ_p_task(influPtask)
                .setChannel(ProviderUtil.FUYOU_CHANNEL)
                .setChange_config(0)
                .setDefault_channel(contractRule.getDefault())
                .setPayway(contractRule.getPayway())
                .setContract_rule(contractRule.getRule())
                .setRule_group_id(event.getRule_group_id())
                .setSchedule_status(ScheduleEnum.SCHEDULE_ENABLE.getValue())
                .setRetry(0);
        if (PaywayEnum.ACQUIRER.getValue().equals(contractRule.getPayway())) {
            Integer taskType = null;
            //银行账户变更
            if (ContractEvent.OPT_TYPE_MERCHANT_SETTLEMENT_ACCOUNTS == event.getEvent_type()) {
                if (contractRule.getPayway() == null || PaywayEnum.ACQUIRER.getValue().equals(contractRule.getPayway())) {
                    Map requestParam = (Map) paramContext.get("cardRequestParam");
                    if (!CollectionUtils.isEmpty(requestParam)) {
                        //银行卡管理服务发起的变更(merchant_bank_account_pre)
                        taskType = ProviderUtil.SUB_TASK_TASK_TYPE_CARD_UPDATE;
                    } else {
                        //dts订阅直接变更(merchant_bank_account)
                        taskType = ProviderUtil.SUB_TASK_TASK_TYPE_SETTLEMENT_ACCOUNTS;
                    }
                }
            } else if (ContractEvent.OPT_TYPE_MERCHANT_BASIC_INFORMATION == event.getEvent_type()) {
                //更新基本信息
                taskType = ProviderUtil.SUB_TASK_TASK_TYPE_BASIC_INFORMATION;
            } else if (ContractEvent.OPT_TYPE_NET_CRM_UPDATE == event.getEvent_type()) {
                String crmUpdate = BeanUtil.getPropString(paramContext, "crmUpdate");
                if (!org.springframework.util.StringUtils.isEmpty(crmUpdate)) {
                    if ("0".equals(crmUpdate)) {
                        taskType = ProviderUtil.SUB_TASK_TASK_TYPE_BASIC_INFORMATION;
                    } else if ("1".equals(crmUpdate)) {
                        taskType = ProviderUtil.SUB_TASK_TASK_TYPE_SETTLEMENT_ACCOUNTS;
                    } else if ("2".equals(crmUpdate)) {
                        taskType = ProviderUtil.SUB_TASK_TASK_TYPE_CRM_BOTH;
                    } else {
                        //do nothing
                    }
                }

            } else if (ContractEvent.OPT_TYPE_UPDATE_BUSINESS_LICENSE == event.getEvent_type()) {
                // 如果富友子商户是小微，则生成变更营业执照任务
                MerchantProviderParamsExample dto = new MerchantProviderParamsExample();
                dto.createCriteria().andMerchant_snEqualTo(merchantSn).andProviderEqualTo(ProviderEnum.PROVIDER_FUYOU.getValue()).andDeletedEqualTo(false);
                List<MerchantProviderParams> paramsList = merchantProviderParamsMapper.selectByExample(dto);
                for (MerchantProviderParams params : paramsList) {
                    if (params.getMerchant_name() != null && params.getMerchant_name().contains("商户_")) {
                        return null;
                    }
                }
                if (PaywayEnum.ACQUIRER.getValue().equals(contractRule.getPayway())) {
                    //更新营业执照
                    taskType = ProviderUtil.SUB_TASK_TASK_TYPE_UPDATE_BUSINESS_LICENSE;
                }
            } else if (ContractEvent.OPT_TYPE_MERCHANT_FEERATE == event.getEvent_type()) {
                ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(event.getMerchant_sn());
                if (wxSettlementIdChangeBiz.isBank(contractStatus.getAcquirer()) && !event.getEvent_msg().contains("app_id")) {
                    return null;
                }
                taskType = ProviderUtil.SUB_TASK_TASK_TYPE_MERCHANT_FEERATE;
            }


            if (taskType == null) {
                return null;
            }
            return subTask.setTask_type(taskType);
        } else {
            Map eventMsg = JSON.parseObject(event.getEvent_msg(), Map.class);
            if (org.springframework.util.CollectionUtils.isEmpty(eventMsg)) {
                log.error("fuyou channelNo {} 更新事件 {} eventMsg为空", contractRule.getChannelNo(), event.getId());
                return null;
            }
            if (WosaiMapUtils.getBooleanValue(eventMsg, ContractEvent.FORCE_UPDATE)) {
                //不允许强制更新云闪付和京东白条
                if (Lists.newArrayList(PaywayEnum.UNIONPAY.getValue(), PaywayEnum.JD_WALLET.getValue()).contains(contractRule.getPayway())) {
                    return null;
                }
                subTask.setTask_type(ProviderUtil.SUB_TASK_TASK_TYPE_UPDATE);
                return subTask;
            }
            List<String> changeFields = (List) eventMsg.get("msg");
            if (!ProviderUtil.SUB_TASK_TASK_TYPE_CRM_BOTH.equals(event.getEvent_type()) && CollectionUtils.isEmpty(changeFields)) {
                log.error("fuyou channelNo {} 更新事件 {} msg为空", contractRule.getChannelNo(), event.getId());
                return null;
            }
            if (ProviderUtil.getPayWayUpdate(contractRule.getPayway(), event.getEvent_type(), changeFields)) {
                subTask.setTask_type(ProviderUtil.SUB_TASK_TASK_TYPE_UPDATE);
                return subTask;
            }
            //云闪付费率是否需要生成变更任务
            if (ContractEvent.OPT_TYPE_MERCHANT_FEERATE == event.getEvent_type()
                    && PaywayEnum.UNIONPAY.getValue().equals(contractRule.getPayway())) {
                //富友记录的云闪付费率和收钱吧的费率做对比,不一致则需要更新
                final Boolean same = fuyouService.unionRateCompare(merchantSn, paramContext);
                if (!same) {
                    return subTask.setTask_type(ProviderUtil.SUB_TASK_TASK_TYPE_MERCHANT_FEERATE);
                }
            }

            //京东是否需要生成变更任务
            if (ContractEvent.OPT_TYPE_MERCHANT_FEERATE == event.getEvent_type()
                    && PaywayEnum.JD_WALLET.getValue().equals(contractRule.getPayway())) {
                //富友记录的云闪付费率和收钱吧的费率做对比,不一致则需要更新
                final Boolean same = fuyouService.jdRateCompare(merchantSn, paramContext);
                if (!same) {
                    return subTask.setTask_type(ProviderUtil.SUB_TASK_TASK_TYPE_MERCHANT_FEERATE);
                }
            }

            return null;
        }
    }


    @Override
    protected ContractResponse processInsertTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask sub) {
        FuyouParam fuyouParam = buildParam(contractChannel, sub, FuyouParam.class);
        Map contextParam = JSON.parseObject(contractTask.getEvent_context(), Map.class);
        ContractRule contractRule = ruleContext.getContractRule(sub.getContract_rule());
        ContractResponse response = null;
        if (contractRule.getType() == 1) {
            if (needPayFor(contextParam, sub, contractTask)) {
                return null;
            }
            response = fuyouService.contractMerchant(contextParam, fuyouParam);
            if (response.isSuccess()) {
                String merchantId = BeanUtil.getPropString(contextParam, "merchant.id");
                updateClearProvider(merchantId, sub.getMerchant_sn());
            }
        } else if (contractRule.getType() == 2) {
            response = fuyouService.applyElectronicAgreement(contractTask.getMerchant_sn(), fuyouParam);
        } else if (contractRule.getType() == 3) {
            MerchantProviderParamsExample dto = new MerchantProviderParamsExample();
            dto.createCriteria().andMerchant_snEqualTo(contractTask.getMerchant_sn()).andPaywayEqualTo(PaywayEnum.ACQUIRER.getValue()).andProviderEqualTo(ProviderEnum.PROVIDER_FUYOU.getValue()).andDeletedEqualTo(false);
            MerchantProviderParams params = merchantProviderParamsMapper.selectByExample(dto).get(0);
            ContractSubTask subTask = contractSubTaskMapper.selectByPrimaryKey(sub.getSchedule_dep_task_id());
            Map<String, Object> singResponse = MapUtils.getMap(subTask.getResponseBody(), "responseParam");
            SignElectronicAgreementRequest request = new SignElectronicAgreementRequest();
            request.setMerchant_sn(contractTask.getMerchant_sn());
            request.setMchnt_cd(params.getProvider_merchant_id());
            request.setContract_no(BeanUtil.getPropString(singResponse, "contract_no"));
            request.setVerify_no(BeanUtil.getPropString(singResponse, "verify_no"));
            response = fuyouService.signElectronicAgreement(request, fuyouParam);
        } else if (contractRule.getType() == 4) {
            response = fuyouService.uploadFilesZip(contextParam, fuyouParam);
        } else if (contractRule.getType() == 5) {
            response = fuyouService.confirmUploadFiles(contractTask.getMerchant_sn(), fuyouParam);
            MerchantProviderParamsExample dto = new MerchantProviderParamsExample();
            dto.createCriteria().andMerchant_snEqualTo(contractTask.getMerchant_sn()).andPaywayEqualTo(PaywayEnum.ACQUIRER.getValue()).andProviderEqualTo(ProviderEnum.PROVIDER_FUYOU.getValue()).andDeletedEqualTo(false);
        } else if (contractRule.getType() == 6) {
            //新增入网，原来逻辑
            Map merchantMap = WosaiMapUtils.getMap(contextParam, "merchant");
            if (Objects.equals(contractTask.getType(), "新增商户入网")) {
                Map mapping = com.wosai.upay.job.config.ApolloConfig.getNetFuYouSpecChNlTypeAndBusinessCodeAndChannelNoMapping(WosaiMapUtils.getString(merchantMap, "industry"));
                response = queryAndProcessFuYouInuseWechatMchId(wechatChannelNo, mapping, contractTask, sub, contextParam, fuyouParam);
                if (!Objects.equals(sub.getChange_config(), 0) && MapUtils.isNotEmpty(mapping) && com.wosai.upay.job.config.ApolloConfig.getFuYouSpecialIndustryApplySwitch()) {
                    sub.setChange_config(0);
                    contractSubTaskMapper.updateByPrimaryKey(sub);
                }
            } else {
                //切换到普通渠道
                response = processIndustryChangeToNormal(sub, contractTask, merchantMap, contextParam, fuyouParam);
            }

        } else if (contractRule.getType() == 7) {
            response = fuyouService.queryMchId(contractTask.getMerchant_sn(), PaywayEnum.ALIPAY.getValue(), fuyouParam);
            Map<String, Object> tradeParam = response.getTradeParam();
            Map merchant = MapUtils.getMap(contextParam, ParamContextBiz.KEY_MERCHANT);
            MerchantProviderParamsExample dto = new MerchantProviderParamsExample();
            dto.createCriteria()
                    .andMerchant_snEqualTo(contractTask.getMerchant_sn())
                    .andPaywayEqualTo(PaywayEnum.ACQUIRER.getValue())
                    .andProviderEqualTo(ProviderEnum.PROVIDER_FUYOU.getValue())
                    .andDeletedEqualTo(false);
            MerchantProviderParams params = merchantProviderParamsMapper.selectByExample(dto).get(0);
            if (response.isSuccess()) {
                Map respParam = response.getResponseParam();
                String payMerchantId = BeanUtil.getPropString(respParam, "sub_mch_id");
                MerchantProviderParams merchantProviderParams = merchantProviderParamsMapper.selectByPayMerchantId(payMerchantId);
                if (merchantProviderParams == null) {
                    String unionMcc = industryMappingCommonBiz.getAliIndirectMcc(BeanUtil.getPropString(merchant, Merchant.INDUSTRY));
                    MerchantProviderParams newParams = new MerchantProviderParams().setId(UUID.randomUUID().toString()).setOut_merchant_sn(params.getMerchant_sn())
                            .setMerchant_name(merchantNameHelper.getMerchantName(contractTask.getMerchant_sn())).setParent_merchant_id(alipayChannelNo)
                            .setChannel_no(alipayChannelNo).setProvider(ProviderEnum.PROVIDER_FUYOU.getValue()).setProvider_merchant_id(params.getProvider_merchant_id()).setPay_merchant_id(payMerchantId).setMerchant_sn(params.getMerchant_sn())
                            .setParams_config_status(MerchantProviderParams.PARAMS_CONFIG_STATUS_NULL).setContract_rule(sub.getContract_rule()).setRule_group_id("fuyou").setPayway(PaywayEnum.ALIPAY.getValue())
                            .setAli_mcc(unionMcc).setCtime(System.currentTimeMillis()).setMtime(System.currentTimeMillis());
                    merchantProviderParamsMapper.insertSelective(newParams);
                    ProviderTerminalContext context = ProviderTerminalContext.builder()
                            .subMerchant(payMerchantId)
                            .provider(ProviderEnum.PROVIDER_FUYOU.getValue())
                            .payWay(PaywayEnum.ALIPAY.getValue())
                            .build();
                    merchantProviderParams = merchantProviderParamsMapper.selectByPayMerchantId(payMerchantId);
                }
                response.setMerchantProviderParamsId(merchantProviderParams.getId());
                tradeParam.put(MerchantProviderTradeParams.PROVIDER_MERCHANT_ID, params.getProvider_merchant_id());
                tradeParam.put(MerchantProviderTradeParams.WEIXIN_MERCHANT_ID, payMerchantId);
                response.setTradeParam(tradeParam);
            } else {
                response.setTradeParam(tradeParam);
            }
        } else if (8 == contractRule.getType()) {
            //是否申请开通了云闪付二维码
            String modifyNo = sub.getContract_id();
            if (StringUtils.isEmpty(modifyNo)) {
                response = fuyouService.mchntOpenUpayQr(sub.getMerchant_sn(), contextParam);
                if (response.isSuccess()) {
                    final MchntOpenUpayQrResponse mchntOpenUpayQrResponse = JSONObject.parseObject(JSONObject.toJSONString(response.getResponseParam()), MchntOpenUpayQrResponse.class);
                    modifyNo = mchntOpenUpayQrResponse.getModifyNo();
                    //存储相关必要信息,用于业务方做业务判断
                    Map<String, Object> tradeParams = Maps.newHashMap();
                    tradeParams.put("contractId", modifyNo);
                    response.setTradeParam(tradeParams);
                }
            } else {//申请成功后->5.1 业务审核状态查询接口-->7.2 商户渠道子商户号查询接口
                final String merchantSn = contractTask.getMerchant_sn();
                //业务审核状态查询接口
                response = fuyouService.mchntQueryTZ(merchantSn, modifyNo, MODIFY_TP_YL);
                //获取当前sub_task的request_body,把这个固定放在response.setRequestParam中为了确保在更新云闪付商户的信息是不会改变mchnt_sub_tp
                response.setRequestParam(sub.getRequestBody());
                if (!response.isSuccess()) {
                    return response;
                }
                // 解析查询响应
                MchntQueryTZResponse mchntQueryTZResponse = JSONObject.parseObject(JSONObject.toJSONString(response.getResponseParam()), MchntQueryTZResponse.class);
                String modifyStatus = mchntQueryTZResponse.getModifyStatus();
                final String modifyStatusDesc = mchntQueryTZResponse.getModifyStatusDesc();
                // 拒绝状态处理
                if (Objects.equals(modifyStatus, REJECTED)) {
                    return response.setCode(Constant.RESULT_CODE_BIZ_EXCEPTION).setMessage(StringUtils.isEmpty(modifyStatusDesc) ? "审核失败" : modifyStatusDesc);
                }
                // 审核中
                if (!Objects.equals(modifyStatus, COMPLETED)) {
                    return response.setCode(Constant.RESULT_CODE_SYSTEM_EXCEPTION).setMessage(StringUtils.isEmpty(modifyStatusDesc) ? "审核中" : modifyStatusDesc);
                }
                //审核通过 --> 商户渠道子商户号查询接口
                response = fuyouService.queryMchId(merchantSn, PaywayEnum.UNIONPAY.getValue(), fuyouParam);
                //获取当前sub_task的request_body,把这个固定放在response.setRequestParam中为了确保在更新云闪付商户的信息是不会改变mchnt_sub_tp
                response.setRequestParam(sub.getRequestBody());
                Map<String, Object> tradeParam = response.getTradeParam();
                if (!response.isSuccess()) {
                    return response;
                }
                //进件富友成功后的信息
                MerchantProviderParamsExample dto = new MerchantProviderParamsExample();
                dto.createCriteria()
                        .andMerchant_snEqualTo(merchantSn)
                        .andPaywayEqualTo(PaywayEnum.ACQUIRER.getValue())
                        .andProviderEqualTo(ProviderEnum.PROVIDER_FUYOU.getValue())
                        .andDeletedEqualTo(false);
                MerchantProviderParams params = merchantProviderParamsMapper.selectByExample(dto).get(0);
                //富友子商户号查询接口返回
                Map respParam = response.getResponseParam();
                String payMerchantId = BeanUtil.getPropString(respParam, "sub_mch_id");
                MerchantProviderParams merchantProviderParams = merchantProviderParamsMapper.selectByPayMerchantId(payMerchantId);
                if (merchantProviderParams == null) {
                    //保存merchant_provider_params
                    MerchantProviderParams newParams = new MerchantProviderParams().setId(UUID.randomUUID().toString()).setOut_merchant_sn(params.getMerchant_sn())
                            .setMerchant_name(merchantNameHelper.getMerchantName(contractTask.getMerchant_sn())).setParent_merchant_id(params.getProvider_merchant_id())
                            .setChannel_no(UNION_CHANNEL_NO).setProvider(ProviderEnum.PROVIDER_FUYOU.getValue()).setProvider_merchant_id(params.getProvider_merchant_id()).setPay_merchant_id(payMerchantId).setMerchant_sn(params.getMerchant_sn())
                            .setParams_config_status(MerchantProviderParams.PARAMS_CONFIG_STATUS_NULL).setContract_rule(sub.getContract_rule()).setRule_group_id("fuyou").setPayway(PaywayEnum.UNIONPAY.getValue())
                            .setCtime(System.currentTimeMillis()).setMtime(System.currentTimeMillis());
                    merchantProviderParamsMapper.insertSelective(newParams);
                    merchantProviderParams = merchantProviderParamsMapper.selectByPayMerchantId(payMerchantId);
                }
                final String merchantProviderParamsId = merchantProviderParams.getId();
                response.setMerchantProviderParamsId(merchantProviderParamsId);
                tradeParam.put(MerchantProviderTradeParams.PROVIDER_MERCHANT_ID, params.getProvider_merchant_id());
                tradeParam.put(MerchantProviderTradeParams.UNIONOPEN_MERCHANT_ID, payMerchantId);
                response.setTradeParam(tradeParam);
                //解决通过收单机构切换入网富友的商户,收单机构切换完毕后云闪付参数才审核通过,没有将云闪付设为默认的现象
                log.info("尝试触发云闪付默认设置 rule_group_id={},匹配结果={}",
                        sub.getRule_group_id(),
                        StrUtil.contains(sub.getRule_group_id(), CHANGE_TO_FUYOU_RULE_GROUP));
                if (StrUtil.contains(sub.getRule_group_id(), CHANGE_TO_FUYOU_RULE_GROUP)) {
                    CompletableFuture.runAsync(() ->
                            {
                                try {
                                    //外层有事务所以需要延迟执行
                                    Thread.sleep(ApolloConfig.getFyUnionDelay());
                                    log.info("开启切换机构入网富友设置云闪付参数");
                                    merchantProviderParamsService.setDefaultMerchantProviderParams(merchantProviderParamsId, null, "切换机构入网富友设置云闪付参数");
                                } catch (Exception e) {
                                    log.error("切换机构入网富友设置云闪付参数失败", e);
                                }
                            }
                            , fuyouUnionPayThreadPoolTaskExecutor);
                }
            }
        } else if (9 == contractRule.getType()) {
            final String merchantSn = contractTask.getMerchant_sn();
            response = fuyouService.jdbtOpen(contractTask.getMerchant_sn(), contextParam);
            Map<String, Object> tradeParam = response.getTradeParam();
            if (!response.isSuccess()) {
                return response;
            }
            //进件富友成功后的信息
            MerchantProviderParamsExample dto = new MerchantProviderParamsExample();
            dto.createCriteria()
                    .andMerchant_snEqualTo(merchantSn)
                    .andPaywayEqualTo(PaywayEnum.ACQUIRER.getValue())
                    .andProviderEqualTo(ProviderEnum.PROVIDER_FUYOU.getValue())
                    .andDeletedEqualTo(false);
            MerchantProviderParams payway0params = merchantProviderParamsMapper.selectByExample(dto).get(0);
            MerchantProviderParams merchantProviderParams = getAndSaveJdPayProviderParams(contractTask, sub, payway0params);
            response.setMerchantProviderParamsId(merchantProviderParams.getId());
            tradeParam.put(MerchantProviderTradeParams.PROVIDER_MERCHANT_ID, payway0params.getProvider_merchant_id());
            response.setTradeParam(tradeParam);
        } else if (10 == contractRule.getType()) {
            // 微信 特殊渠道，行业报名
            //优先在中间表中获取
            Map merchantMap = WosaiMapUtils.getMap(contextParam, "merchant");
            Map<String, String> mapping = com.wosai.upay.job.config.ApolloConfig.getNetFuYouSpecChNlTypeAndBusinessCodeAndChannelNoMapping(WosaiMapUtils.getString(merchantMap, "industry"));
            return processIndustryChangeToSpec(sub, contextParam, fuyouParam, mapping, merchantMap, contractTask);
        }
        return response;

    }

    private ContractResponse processIndustryChangeToSpec(ContractSubTask sub, Map contextParam, FuyouParam fuyouParam, Map<String, String> mapping, Map merchantMap, ContractTask contractTask) {
        ContractResponse response = null;
        if (StringUtils.isEmpty(sub.getContract_id())) {
            response = handleWechatApplyIndustryActivity(sub, contextParam, contractTask);
        } else {
            response = handleBusinessAuditAndModifyStatus(sub, contextParam, fuyouParam, mapping, merchantMap, contractTask);
        }
        return response;
    }

    private ContractResponse handleWechatApplyIndustryActivity(ContractSubTask sub, Map contextParam, ContractTask contractTask) {
        ContractResponse response = fuyouService.wechatApplyIndustryActivity(sub.getMerchant_sn(), null, contextParam);
        if (!response.isSuccess()) {
            if (!Objects.equals(contractTask.getType(), "新增商户入网")) {
                if (response.isBusinessFail()) {
                    chatBotUtil.sendMessageToFuYouFeeRateExceptionChatBot("merchantSn:" + sub.getMerchant_sn() + "切换特殊行业报名失败，失败原因：" + response.getMessage());
                }
            }
        }
        return response;
    }

    private ContractResponse handleBusinessAuditAndModifyStatus(ContractSubTask sub, Map contextParam, FuyouParam fuyouParam, Map<String, String> mapping, Map merchantMap, ContractTask contractTask) {
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or()
                .andPaywayEqualTo(PaywayEnum.ACQUIRER.getValue())
                .andMerchant_snEqualTo(sub.getMerchant_sn())
                .andDeletedEqualTo(false)
                .andProviderEqualTo(ProviderEnum.PROVIDER_FUYOU.getValue());
        List<MerchantProviderParams> merchantProviderParamList = merchantProviderParamsMapper.selectByExampleWithBLOBs(example);
        if (CollectionUtils.isEmpty(merchantProviderParamList)) {
            throw new CommonPubBizException("富友商户号为空，还未入网");
        }
        ContractResponse contractResponse = fuyouService.queryBusinessAuditStatus(sub.getMerchant_sn(),
                merchantProviderParamList.get(0).getProvider_merchant_id(),
                sub.getContract_id(),
                FuYouConstant.APPLY_AUDIT_QUERY_WECHAT_SP);

        if (!contractResponse.isSuccess()) {
            if (contractResponse.isBusinessFail()) {
                chatBotUtil.sendMessageToFuYouFeeRateExceptionChatBot("merchantSn:" + sub.getMerchant_sn() + "查询申请状态失败，失败原因：" + contractResponse.getMessage());
            }
            return contractResponse;
        }

        if (StringUtils.isEmpty(WosaiMapUtils.getString(mapping, FuYouConstant.CHANNEL_NO))) {
            throw new CommonPubBizException("该商户行业:" + WosaiMapUtils.getString(merchantMap, "industry") + ",不能特殊行业报名");
        }

        updateContractResponseWithContractId(contractResponse, sub.getContract_id());

        String modifyStatus = WosaiMapUtils.getString(contractResponse.getResponseParam(), FuYouConstant.MODIFY_ST);
        if (Objects.equals(FuYouConstant.APPLY_AUDIT_STATUS_04, modifyStatus)) {
            return handleAuditSuccess(sub, fuyouParam, contractResponse, contextParam, mapping, merchantProviderParamList.get(0), merchantMap, contractTask);
        } else if (Objects.equals(FuYouConstant.APPLY_AUDIT_STATUS_00, modifyStatus)) {
            handleAuditFailure(contractResponse, sub.getMerchant_sn(), contractTask);
        }
        return contractResponse;
    }


    private MerchantProviderParams processMerchantProviderParamsIfMchIdIsNotEmpty(String mchId, Map<String, String> mapping, Map contextParam, FuyouParam fuyouParam, Map merchantMap, ContractTask contractTask, ContractSubTask sub, MerchantProviderParams merchantProviderParam) {
        MerchantProviderParams merchantProviderParams = null;
        if (!StringUtils.isEmpty(mchId)) {
            merchantProviderParams = processMerchantProviderParams(mchId, WosaiMapUtils.getString(mapping, FuYouConstant.CHANNEL_NO), mapping, contextParam, merchantMap, contractTask, sub, merchantProviderParam);
        }
        return merchantProviderParams;
    }

    private void updateContractResponseWithContractId(ContractResponse contractResponse, String contractId) {
        if (MapUtils.isEmpty(contractResponse.getTradeParam())) {
            contractResponse.setTradeParam(CollectionUtil.hashMap("contractId", contractId));
        }
        if (StringUtils.isEmpty(WosaiMapUtils.getString(contractResponse.getTradeParam(), "contractId"))) {
            contractResponse.getTradeParam().put("contractId", contractId);
        }
    }

    private ContractResponse handleAuditSuccess(ContractSubTask sub, FuyouParam fuyouParam, ContractResponse contractResponse, Map contextParam, Map mapping, MerchantProviderParams acquirerMerchantProviderParams, Map merchantMap, ContractTask contractTask) {
        ContractResponse queryMchIdResponse = fuyouService.queryIndustryMchId(sub.getMerchant_sn(), PaywayEnum.WEIXIN.getValue(), fuyouParam, true);
        if (!queryMchIdResponse.isSuccess()) {
            if (queryMchIdResponse.isBusinessFail()) {
                chatBotUtil.sendMessageToFuYouFeeRateExceptionChatBot("商户" + sub.getMerchant_sn() + "富友特殊行业入网失败，查询行业子商号失败，原因：" + queryMchIdResponse.getMessage());
            }
            return queryMchIdResponse;
        }
        String fuYouInUseMchId = WosaiMapUtils.getString(queryMchIdResponse.getResponseParam(), FuYouConstant.SUB_MCH_ID);

        if (!StringUtils.isEmpty(fuYouInUseMchId)) {
//            assert merchantProviderParams != null;
            MerchantProviderParams merchantProviderParams = processMerchantProviderParamsIfMchIdIsNotEmpty(fuYouInUseMchId, mapping, contextParam, fuyouParam, merchantMap, contractTask, sub, acquirerMerchantProviderParams);
            queryMchIdResponse.setMerchantProviderParamsId(merchantProviderParams.getId());
            contractResponse.getTradeParam().put("contractId", "");
            contractResponse.setMerchantProviderParamsId(merchantProviderParams.getId());

            if (Objects.equals(contractTask.getType(), "新增商户入网")) {
                merchantProviderParamsDAO.deleteFuYouWechatMchIdExceptSpecifiedOne(sub.getMerchant_sn(), fuYouInUseMchId);
            } else {
                Optional<MerchantProviderParamsDO> merchantProviderParamsDO = merchantProviderParamsDAO.getMerchantProviderParamsByPayMerchantId(fuYouInUseMchId);
                changeTradeParamsAndDeleteOtherChannelNoParams(MerchantProviderParamsConverter.merchantProviderParamsDOToMerchantProviderParams(merchantProviderParamsDO.get()));
                //同步修改行业
                centerMerchantService.copyMcInfoToMerchantByMerchantId(WosaiMapUtils.getString(merchantMap, "id"), devCode);
            }
        }
        return contractResponse;
    }

    private void handleAuditFailure(ContractResponse contractResponse, String merchantSn, ContractTask contractTask) {
        contractResponse.setCode(400);
        String message = WosaiMapUtils.getString(contractResponse.getResponseParam(), FuYouConstant.MODIFY_ST_DESC) + ";"
                + WosaiMapUtils.getString(contractResponse.getResponseParam(), FuYouConstant.MODIFY_DEAL_MSG);
        contractResponse.setMessage(message);

        if (!Objects.equals(contractTask.getType(), "新增商户入网")) {
            chatBotUtil.sendMessageToFuYouFeeRateExceptionChatBot("merchantSn:" + merchantSn + "审核失败，失败原因：" + message);
        }
    }

    @NotNull
    private ContractResponse processIndustryChangeToNormal(ContractSubTask sub, ContractTask contractTask, Map merchantMap, Map contextParam, FuyouParam fuyouParam) {
        if (StringUtils.isEmpty(sub.getContract_id())) {
            ContractResponse contractResponse = fuyouService.cancelWechatSpecIndustryActivity(sub.getMerchant_sn());
            String retCode = WosaiMapUtils.getString(contractResponse.getResponseParam(), RET_CODE);
            if (!contractResponse.isSuccess() && !Objects.equals(retCode, MERCHANT_DID_NOT_ATTEND_INDUSTRY_EVENTS_ERRCODE)) {
                //取消失败直接返回 + 告警
                if (contractResponse.isBusinessFail()) {
                    chatBotUtil.sendMessageToFuYouFeeRateExceptionChatBot("merchantSn:" + sub.getMerchant_sn() + ",富友行业切换普通渠道取消活动失败");
                }
                return contractResponse;
            }
        }
        //查询普通渠道子商号
        ContractResponse normalWechatSubMchIdContractResponse = fuyouService.queryMchId(sub.getMerchant_sn(), PaywayEnum.WEIXIN.getValue(), fuyouParam);
        if (!normalWechatSubMchIdContractResponse.isSuccess()) {
            if (normalWechatSubMchIdContractResponse.isBusinessFail()) {
                chatBotUtil.sendMessageToFuYouFeeRateExceptionChatBot("merchantSn:" + sub.getMerchant_sn() + ",富友行业切换普通渠道取消活动失败");
            }
            return normalWechatSubMchIdContractResponse;
        }
        Map<String, Object> responseParam = normalWechatSubMchIdContractResponse.getResponseParam();
        String subMchId = WosaiMapUtils.getString(responseParam, FuYouConstant.SUB_MCH_ID);
        if (StringUtils.isEmpty(subMchId)) {
            // 发送消息并设置响应
            chatBotUtil.sendMessageToFuYouFeeRateExceptionChatBot("merchantSn:" + sub.getMerchant_sn() + ",富友行业切换普通渠道取消活动失败，没找到逻辑删除的普通渠道子商号");
            normalWechatSubMchIdContractResponse.setCode(400);
            normalWechatSubMchIdContractResponse.setMessage("没找到逻辑删除的普通渠道子商号");
            return normalWechatSubMchIdContractResponse;
        }
        Optional<MerchantProviderParamsDO> merchantProviderParamsOptional = merchantProviderParamsDAO.getSpecifiedMchId(subMchId);
        if (merchantProviderParamsOptional.isPresent()) {
            MerchantProviderParamsDO merchantProviderParams = merchantProviderParamsOptional.get();
            String channelNo = merchantProviderParams.getChannelNo();

            if (Objects.equals(channelNo, wechatChannelNo)) {
                // 恢复普通渠道子商号
                merchantProviderParams.setDeleted(0);
                merchantProviderParamsDAO.updateByPrimaryKey(merchantProviderParams);

                // 获取并处理子商号
                Optional<MerchantProviderParamsDO> optional = merchantProviderParamsDAO.getMerchantProviderParamsByPayMerchantId(subMchId);
                optional.ifPresent(e -> changeTradeParamsAndDeleteOtherChannelNoParams(MerchantProviderParamsConverter.merchantProviderParamsDOToMerchantProviderParams(e)));
                // 同步行业信息
                centerMerchantService.copyMcInfoToMerchantByMerchantId(WosaiMapUtils.getString(merchantMap, "id"), devCode);
            } else {
                // 更新 tradeParam ，进行下一轮执行
                Map<String, Object> tradeParam = normalWechatSubMchIdContractResponse.getTradeParam();
                tradeParam.put("contractId", wechatChannelNo);
            }
        } else {
            //子商号存储
            MerchantProviderParams merchantProviderParam = getAcquirerMerchantProviderParam(sub.getMerchant_sn());
            MerchantProviderParams merchantProviderParams = processMerchantProviderParams(subMchId, wechatChannelNo, null, contextParam, merchantMap, contractTask, sub, merchantProviderParam);
            changeTradeParamsAndDeleteOtherChannelNoParams(merchantProviderParams);
            normalWechatSubMchIdContractResponse.setMerchantProviderParamsId(merchantProviderParams.getId());
            normalWechatSubMchIdContractResponse.getTradeParam().put(MerchantProviderTradeParams.PROVIDER_MERCHANT_ID, merchantProviderParams.getProvider_merchant_id());
            normalWechatSubMchIdContractResponse.getTradeParam().put(MerchantProviderTradeParams.WEIXIN_MERCHANT_ID, subMchId);
            centerMerchantService.copyMcInfoToMerchantByMerchantId(WosaiMapUtils.getString(merchantMap, "id"), devCode);
        }
        return normalWechatSubMchIdContractResponse;
    }

    private MerchantProviderParams getAcquirerMerchantProviderParam(String merchantSn) {
        MerchantProviderParamsExample dto = new MerchantProviderParamsExample();
        dto.createCriteria().andMerchant_snEqualTo(merchantSn).andPaywayEqualTo(PaywayEnum.ACQUIRER.getValue()).andProviderEqualTo(ProviderEnum.PROVIDER_FUYOU.getValue()).andDeletedEqualTo(false);
        List<MerchantProviderParams> paramList = merchantProviderParamsMapper.selectByExample(dto);
        if (CollectionUtils.isEmpty(paramList)) {
            throw new CommonPubBizException("商户还未入网");
        }
        return paramList.get(0);
    }

    private void changeTradeParamsAndDeleteOtherChannelNoParams(MerchantProviderParams params) {
        try {
            // 独立行业变更这个时候还没有写回原表,所以切参数时候不校验结算ID是否匹配
            ThreadLocalUtil.setCheckSettlement(false);
            boolean isChangedTradeParams = tradeParamsBiz.changeTradeParams(params, null, Boolean.FALSE, subBizParamsBiz.getPayTradeAppId());
            if (!isChangedTradeParams) {
                throw new CommonPubBizException("切换交易参数失败");
            }
            //删除除上述的子商号
            merchantProviderParamsDAO.deleteFuYouWechatMchIdExceptSpecifiedOne(params.getMerchant_sn(), params.getPay_merchant_id());
        } finally {
            ThreadLocalUtil.clearCheckSettlement();
        }
    }

    private ContractResponse queryAndProcessFuYouInuseWechatMchId(String channelNo, Map mapping, ContractTask contractTask, ContractSubTask sub, Map contextParam, FuyouParam fuyouParam) {
        ContractResponse response = fuyouService.queryMchId(contractTask.getMerchant_sn(), PaywayEnum.WEIXIN.getValue(), fuyouParam);
        Map<String, Object> tradeParam = response.getTradeParam();
        Map merchant = MapUtils.getMap(contextParam, "merchant");
        MerchantProviderParams params = getAcquirerMerchantProviderParam(contractTask.getMerchant_sn());
        if (response.isSuccess()) {
            Map respParam = response.getResponseParam();
            String payMerchantId = BeanUtil.getPropString(respParam, FuYouConstant.SUB_MCH_ID);
            MerchantProviderParams merchantProviderParams = processMerchantProviderParams(payMerchantId, channelNo, mapping, contextParam, merchant, contractTask, sub, params);
            response.setMerchantProviderParamsId(merchantProviderParams.getId());
            tradeParam.put(MerchantProviderTradeParams.PROVIDER_MERCHANT_ID, params.getProvider_merchant_id());
            tradeParam.put(MerchantProviderTradeParams.WEIXIN_MERCHANT_ID, payMerchantId);
            response.setTradeParam(tradeParam);
        } else {
            response.setTradeParam(tradeParam);
        }
        return response;
    }

    private MerchantProviderParams processMerchantProviderParams(String payMerchantId, String channelNo, Map mapping, Map contextParam, Map merchant, ContractTask contractTask, ContractSubTask sub, MerchantProviderParams params) {
        MerchantProviderParams merchantProviderParams = merchantProviderParamsMapper.selectByPayMerchantId(payMerchantId);
        if (merchantProviderParams == null) {
            Map license = MapUtils.getMap(contextParam, "merchantBusinessLicense");
            String merchantName = merchantNameHelper.getMerchantName(contractTask.getMerchant_sn());
            String settlementId = wechatAuthBiz.getSettlementId(getIndustryId(mapping, channelNo, merchant), BeanUtil.getPropInt(license, MerchantBusinessLicence.TYPE), merchantName);
            MerchantProviderParams newParams = new MerchantProviderParams().setId(UUID.randomUUID().toString()).setMerchant_name(merchantName)
                    .setOut_merchant_sn(params.getMerchant_sn()).setParent_merchant_id(channelNo)
                    .setChannel_no(channelNo).setProvider(ProviderEnum.PROVIDER_FUYOU.getValue()).setProvider_merchant_id(params.getProvider_merchant_id()).setPay_merchant_id(payMerchantId).setMerchant_sn(params.getMerchant_sn())
                    .setParams_config_status(MerchantProviderParams.PARAMS_CONFIG_STATUS_PRE).setContract_rule(sub.getContract_rule()).setRule_group_id("fuyou").setPayway(PaywayEnum.WEIXIN.getValue())
                    .setWx_settlement_id(settlementId).setCtime(System.currentTimeMillis()).setMtime(System.currentTimeMillis());
            int insert = merchantProviderParamsMapper.insertSelective(newParams);
            merchantProviderParams = merchantProviderParamsMapper.selectByPayMerchantId(payMerchantId);
        }
        return merchantProviderParams;
    }

    private String getIndustryId(Map mapping, String channelNo, Map merchantMap) {
        String industryId = null;
        if (Objects.nonNull(mapping) && Objects.equals(channelNo, wechatChannelNo)) {
            industryId = ApolloConfig.getSpecifiedFuYouNormalIndustry();
        } else {
            industryId = WosaiMapUtils.getString(merchantMap, "industry");
        }
        return industryId;
    }

    private MerchantProviderParams getAndSaveJdPayProviderParams(ContractTask contractTask, ContractSubTask sub, MerchantProviderParams params) {
        MerchantProviderParams merchantProviderParams = merchantProviderParamsMapper.getParamsByProviderMerchantIdAndProviderAndPayWay(params.getProvider_merchant_id(),
                String.valueOf(ProviderEnum.PROVIDER_FUYOU.getValue()),
                String.valueOf(PaywayEnum.JD_WALLET.getValue())
        );
        if (merchantProviderParams == null) {
            //保存merchant_provider_params
            MerchantProviderParams newParams = new MerchantProviderParams()
                    .setId(UUID.randomUUID().toString())
                    .setOut_merchant_sn(params.getMerchant_sn())
                    .setMerchant_name(merchantNameHelper.getMerchantName(contractTask.getMerchant_sn()))
                    .setParent_merchant_id(params.getProvider_merchant_id())
                    .setChannel_no("fuyou")
                    .setProvider(ProviderEnum.PROVIDER_FUYOU.getValue())
                    .setProvider_merchant_id(params.getProvider_merchant_id())
                    .setPay_merchant_id(null)
                    .setMerchant_sn(params.getMerchant_sn())
                    .setParams_config_status(MerchantProviderParams.PARAMS_CONFIG_STATUS_NULL)
                    .setContract_rule(sub.getContract_rule())
                    .setRule_group_id("fuyou")
                    .setPayway(PaywayEnum.JD_WALLET.getValue())
                    .setCtime(System.currentTimeMillis())
                    .setMtime(System.currentTimeMillis());
            merchantProviderParamsMapper.insertSelective(newParams);
            merchantProviderParams = merchantProviderParamsMapper.getParamsByProviderMerchantIdAndProviderAndPayWay(params.getProvider_merchant_id(),
                    String.valueOf(ProviderEnum.PROVIDER_FUYOU.getValue()),
                    String.valueOf(PaywayEnum.JD_WALLET.getValue())
            );
        }
        return merchantProviderParams;
    }

    private boolean isDoubleAccount(Integer accountType, Integer businessType) {
        if (Objects.isNull(accountType) || Objects.isNull(businessType)) {
            return false;
        }
        // 富友 企业对私 -> 双账户
        return BankAccountTypeEnum.isPersonal(accountType) && businessType > BusinessLicenseTypeEnum.INDIVIDUAL.getValue();
    }

    @Override
    protected ContractResponse processUpdateTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask sub) {

        FuyouParam fuyouParam = buildParam(contractChannel, sub, FuyouParam.class);
        Map contextParam = JSON.parseObject(contractTask.getEvent_context(), Map.class);
        Map merchant = (Map) contextParam.get(ParamContextBiz.KEY_MERCHANT);
        Map bankAccount = (Map) contextParam.get(ParamContextBiz.KEY_BANK_ACCOUNT);
        Map license = (Map) contextParam.get(ParamContextBiz.KEY_BUSINESS_LICENCE);
        if (needPayFor(contextParam, sub, contractTask)) {
            return null;
        }
        MerchantProviderParamsExample dto = new MerchantProviderParamsExample();
        dto.createCriteria().andMerchant_snEqualTo(contractTask.getMerchant_sn()).andPaywayEqualTo(PaywayEnum.ACQUIRER.getValue()).andProviderEqualTo(ProviderEnum.PROVIDER_FUYOU.getValue()).andDeletedEqualTo(false);
        MerchantProviderParams merchantProviderParams = merchantProviderParamsMapper.selectByExample(dto).get(0);
        ContractResponse tarResponse = null;
        Integer taskType = sub.getTask_type();
        // 查询当前在用的银行卡信息
        com.wosai.upay.bank.model.MerchantBankAccount account = bankBusinessLicenseService.getMerchantBankAccountByMerchantId(BeanUtil.getPropString(merchant, DaoConstants.ID));
        if (ProviderUtil.SUB_TASK_TASK_TYPE_BASIC_INFORMATION.equals(taskType)) {
            contextParam.put(CommonConstant.SQB_FEE_RATES, new ArrayList<>());
            return fuyouService.updateBasicMerchant(contextParam, fuyouParam);
        } else if (ProviderUtil.SUB_TASK_TASK_TYPE_SETTLEMENT_ACCOUNTS.equals(taskType)
                || ProviderUtil.SUB_TASK_TASK_TYPE_CARD_UPDATE.equals(taskType)
                || ProviderUtil.SUB_TASK_TASK_TYPE_CRM_BOTH.equals(taskType)) {
            // 企业对私，调用富友4.2.2接口修改银行卡信息
            if ((bankAccount != null && BankAccountTypeEnum.isPersonal(BeanUtil.getPropInt(bankAccount, "type")))
                    && (license != null && BeanUtil.getPropInt(license, "type") > BusinessLicenseTypeEnum.INDIVIDUAL.getValue())) {
                if (BankAccountTypeEnum.isPublic(BeanUtil.getPropInt(account, "type"))) {
                    MerchantInfo merchantInfo = saveAcceptanceCertificate(contractTask.getMerchant_sn(), account);
                    contextParam.put("merchant", JSON.parseObject(JSON.toJSONString(merchantInfo), Map.class));
                }
                tarResponse = fuyouService.changeOfAccount(contextParam, fuyouParam);
            } else if (bankAccount != null && (BeanUtil.getPropInt(bankAccount, "type") != BeanUtil.getPropInt(account, "type"))) {
                // 对公转对私，对私转对公，调用富友4.2.2接口
                if (BankAccountTypeEnum.isPublic(BeanUtil.getPropInt(account, "type"))) {
                    MerchantInfo merchantInfo = saveAcceptanceCertificate(contractTask.getMerchant_sn(), account);
                    contextParam.put("merchant", JSON.parseObject(JSON.toJSONString(merchantInfo), Map.class));
                }
                tarResponse = fuyouService.changeOfAccount(contextParam, fuyouParam);
            } else {
                contextParam.put(CommonConstant.SQB_FEE_RATES, new ArrayList<>());
                if (applicationApolloConfig.getFuyouNewUpdateBankAccountSwitch()) {
                    tarResponse = fuyouService.updateBankAccount(contextParam, fuyouParam);
                } else {
                    tarResponse = fuyouService.updateMerchant(contextParam, fuyouParam);
                }
            }
        } else if (ProviderUtil.SUB_TASK_TASK_TYPE_MERCHANT_FEERATE.equals(taskType)) {
            if (Objects.equals(PaywayEnum.ACQUIRER.getValue(), sub.getPayway()) || Objects.isNull(sub.getPayway())) {
                tarResponse = fuyouService.updateMerchantFeeRate(contextParam, fuyouParam);
            }
            //云闪付费率变更
            if (Objects.equals(PaywayEnum.UNIONPAY.getValue(), sub.getPayway())) {
                tarResponse = fuyouService.mchntOpenUpayQr(sub.getMerchant_sn(), contextParam);
            }

            //白条费率变更
            if (Objects.equals(PaywayEnum.JD_WALLET.getValue(), sub.getPayway())) {
                tarResponse = fuyouService.jdbtOpen(sub.getMerchant_sn(), contextParam);
            }

        } else if (ProviderUtil.SUB_TASK_TASK_TYPE_STATUS_UPDATE.equals(taskType)) {
            contextParam.put(CommonConstant.SQB_FEE_RATES, new ArrayList<>());
            tarResponse = fuyouService.updateBasicMerchant(contextParam, fuyouParam);
        } else if (ProviderUtil.SUB_TASK_TASK_TYPE_UPDATE_BUSINESS_LICENSE.equals(taskType)) {
            contextParam.put(CommonConstant.SQB_FEE_RATES, new ArrayList<>());
            MerchantBusinessLicense existedBusinessLicense = bankBusinessLicenseService.getBusinessLicenseByMerchantId(MapUtils.getString(merchant, DaoConstants.ID));
            boolean isExistedDoubleAccount = isDoubleAccount(account.getType(), Objects.isNull(existedBusinessLicense) ? BusinessLicenseTypeEnum.MICRO.getValue() : existedBusinessLicense.getType());
            boolean isNewDoubleAccount = isDoubleAccount(MapUtils.getInteger(bankAccount, MerchantBankAccount.TYPE), MapUtils.getInteger(license, MerchantBusinessLicence.TYPE));
            // 都是双账户或者双账户切单账户，走4.2.4
            if (applicationApolloConfig.getFuyouDoubleAccountSwitch()
                    && isExistedDoubleAccount) {
                tarResponse = updateBusinessInfoByManualReview(contractTask, sub, merchantProviderParams, contextParam, fuyouParam);
            } else {
                Integer newAccountType = org.apache.commons.collections4.MapUtils.getInteger(bankAccount, MerchantBankAccount.TYPE);
                String newAccountNumber = org.apache.commons.collections4.MapUtils.getString(bankAccount, MerchantBankAccount.NUMBER);
                Integer oldAccountType = org.apache.commons.collections4.MapUtils.getInteger(contextParam, LicenseUpdateToAcquirerTaskHandler.ORIGINAL_BANK_ACCOUNT_TYPE_KEY);
                String oldAccountNumber = org.apache.commons.collections4.MapUtils.getString(contextParam, LicenseUpdateToAcquirerTaskHandler.ORIGINAL_BANK_ACCOUNT_NO_KEY);
                if (isUpdateLicenseNeedUpdateAccount(contextParam)
                        && fuYouAcquirerFacade.enableUpdateLicenseAndAccountTogether(contractTask.getMerchant_sn(), newAccountType, newAccountNumber, oldAccountType, oldAccountNumber)) {
                    tarResponse = fuyouService.updateLicenseAndBankAccount(contextParam, fuyouParam);
                } else {
                    tarResponse = fuyouService.updateMerchantBusinessLicense(contextParam, fuyouParam);
                }
            }
        } else if (Objects.equals(ContractSubTaskTypeEnum.ATTACHMENT_UPLOAD.getValue(), taskType)) {
            tarResponse = fuyouService.uploadFilesZip(contextParam, fuyouParam);
        } else {
            contextParam.put(CommonConstant.SQB_FEE_RATES, new ArrayList<>());
            tarResponse = fuyouService.updateMerchant(contextParam, fuyouParam);
        }
        if (ProviderUtil.SUB_TASK_TASK_TYPE_SETTLEMENT_ACCOUNTS.equals(sub.getTask_type())
                || ProviderUtil.SUB_TASK_TASK_TYPE_CARD_UPDATE.equals(sub.getTask_type())) {
            Map tradeParam = tarResponse.getTradeParam();
            Map responseParam = tarResponse.getResponseParam();
            // 如果不是实时生效的结算，则回填contractId
            if (!Objects.equals(EFFECTIVE_ACNT, BeanUtil.getPropString(responseParam, "is_effective_acnt", ""))) {
                // 等待次日生效的任务，如果存在modify_no则需要根据modify_no查询结果，如果不存在则只需要等次日轮询生效
                tradeParam.put("contractId", BeanUtil.getPropString(responseParam, "modify_no"));
                if (StringUtil.empty(BeanUtil.getPropString(tradeParam, "contractId"))) {
                    tradeParam.put("contractId", BeanUtil.getPropString(merchantProviderParams, "provider_merchant_id"));
                }
            }
            tarResponse.setTradeParam(tradeParam);
        }
        return tarResponse;
    }

    private ContractResponse updateBusinessInfoByManualReview(ContractTask contractTask, ContractSubTask sub,
                                                              MerchantProviderParams merchantProviderParams,
                                                              Map contextParam, FuyouParam fuyouParam) {
        ContractResponse tarResponse;
        log.info("富友双账户商户变更营业执照信息，走人工审核, merchantSn:{}, subTaskId: {}", contractTask.getMerchant_sn(), sub.getId());
        tarResponse = fuyouService.updateBasicMerchantByManualReview(merchantProviderParams.getProvider_merchant_id(), contextParam, fuyouParam, null);
        // 如果成功，有回调id，插入一个内部调度任务
        if (tarResponse.isSuccess()) {
            Map<String, Object> tradeParam = tarResponse.getTradeParam();
            String contractId = MapUtils.getString(tradeParam, "contractId");
            if (StringUtils.isBlank(contractId)) {
                log.error("富友双账户商户变更营业执照信息，走人工审核，回调id为空，merchantSn:{}, subTaskId: {}", contractTask.getMerchant_sn(), sub.getId());
            }
            RotationalTaskContext rotationalTaskContext = RotationalTaskContext.builder()
                    .merchantSn(contractTask.getMerchant_sn())
                    .contractTaskId(contractTask.getId())
                    .contractSubTaskId(sub.getId())
                    .rotationId(contractId)
                    .subTaskTypeEnum(RotationalSubTaskTypeEnum.FU_YOU_LICENSE_MANUAL_AUDIT)
                    .belongToContractTask(true)
                    .addParam(FuYouBusinessLicenseManualAuditSubTaskProcessor.PROVIDER_MERCHANT_ID, merchantProviderParams.getProvider_merchant_id())
                    .build();
            rotationalTask.buildTaskForContractTask(rotationalTaskContext);
        }
        return tarResponse;
    }

    @Override
    public void createProviderTerminal(String merchantSn, Integer provider) {
    }


    @Override
    public void handleSqbStoreTerminal(String storeSn, String merchantSn, Integer provider) {
        MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_FUYOU.getValue());
        if (Objects.isNull(acquirerParams)) {
            log.error("商户未进件成功,终端绑定失败 {}", merchantSn);
            return;
        }
        ProviderTerminal providerTerminal = providerTerminalBiz.existStoreProviderTerminal(provider, storeSn, merchantSn, acquirerParams.getPay_merchant_id());
        //不存在则绑定
        if (Objects.isNull(providerTerminal)) {
            ContractResponse response = addTerminalToFuyou(merchantSn, acquirerParams.getPay_merchant_id(), storeSn, storeSn);
            if (response.isSuccess()) {
                Map responseParam = response.getResponseParam();
                if ("0000".equals(BeanUtil.getPropString(responseParam, "ret_code"))) {
                    //新增 provider_terminal表
                    String providerTerminalId = BeanUtil.getPropString(responseParam, "fy_term_id");
                    ProviderTerminal terminal = new ProviderTerminal();
                    terminal.setMerchant_sn(merchantSn);
                    terminal.setProvider(ProviderEnum.PROVIDER_FUYOU.getValue());
                    terminal.setProvider_terminal_id(providerTerminalId);
                    terminal.setAcquirer_merchant_id(acquirerParams.getPay_merchant_id());
                    terminal.setStore_sn(storeSn);
                    terminalMapper.insertSelective(terminal);
                    //调用交易接
                    createOrUpdateTradeExtConfig(storeSn, providerTerminalId, TradeExtConfigCreateRequest.SN_TYPE_STORE);
                }
            }
        }
    }

    @Override
    public ContractResponse queryMerchantContractResult(String providerMerchantId) {
        FuyouParam fuyouParam = contractParamsBiz.buildContractParams(ChannelEnum.FUYOU.getValue(), FuyouParam.class);
        Optional<MerchantProviderParamsDO> merchantProviderParamsByProviderMerchantIdAndPayway = merchantProviderParamsDAO.getMerchantProviderParamsByProviderMerchantIdAndPayway(providerMerchantId, PaywayEnum.ACQUIRER.getValue());
        if (!merchantProviderParamsByProviderMerchantIdAndPayway.isPresent()) {
            return ContractResponse.builder().code(460).message("商户还未入网").build();
        }
        String merchantSn = merchantProviderParamsByProviderMerchantIdAndPayway.get().getMerchantSn();
        //如果云闪付进件任务没有执行完,则不允许直接调用fuyouService.queryMchId方法
        final List<ContractSubTask> taskList = contractSubTaskMapper.getSubTasksByMerchantSnAndChannel(merchantSn, ProviderUtil.FUYOU_CHANNEL);
        //云闪付参数
        List<ContractSubTask> filteredTasks = Optional.ofNullable(taskList)
                .orElseGet(ArrayList::new)
                .stream()
                .filter(Objects::nonNull)
                .filter(sub -> Objects.equals(sub.getPayway(), PaywayEnum.UNIONPAY.getValue()))
                .collect(Collectors.toList());
        final Optional<ContractSubTask> successUnionPay = filteredTasks.stream()
                .filter(sub -> sub.getStatus().equals(ContractSubTaskProcessStatusEnum.PROCESS_SUCCESS.getValue()))
                .findFirst();
        //已经有成功也有进行中的场景.有且仅有进行中的任务才返回系统异常
        if (!successUnionPay.isPresent()) {
            final Optional<ContractSubTask> unionContractSubTask = filteredTasks.stream()
                    .filter(sub ->
                            !Objects.equals(ContractSubTaskProcessStatusEnum.PROCESS_FAIL.getValue(), sub.getStatus()))
                    .sorted(Comparator.comparing(ContractSubTask::getCreate_at).reversed())
                    .findFirst();
            if (unionContractSubTask.isPresent()) {
                return ContractResponse.builder()
                        .code(Constant.RESULT_CODE_SYSTEM_EXCEPTION)
                        .message("银联商户报备中，请您稍后重试开通富友一体化刷卡业务，若超过60分钟仍未成功，请联系销售支持处理！")
                        .build();
            }
        }
        ContractResponse contractResponse = fuyouService.queryMchId(merchantSn, PaywayEnum.UNIONPAY.getValue(), fuyouParam);
        if (contractResponse.isSuccess()) {
            contractResponse.setTradeParam(CollectionUtil.hashMap("status", MerchantProviderParamsExtDO.UNION_PAY_SUCCESS));
        }
        return contractResponse;
    }

    @Override
    public ContractResponse addTerminal(ContractTask task, ContractSubTask subTask) {
        String vendorAppAppid = null;
        String tradeSn;
        String storeSn = null;
        String terminalSn = null;
        Integer snType;
        if (StringUtil.empty(task.getEvent_context())) {
            tradeSn = task.getMerchant_sn();
            snType = TradeExtConfigCreateRequest.SN_TYPE_MERCHANT;
        } else {
            Map contextParam = JSON.parseObject(task.getEvent_context(), Map.class);
            Map data = WosaiMapUtils.getMap(contextParam, "data");
            tradeSn = BeanUtil.getPropString(contextParam, "terminalSn");
            vendorAppAppid = BeanUtil.getPropString(data, "vendorAppAppId");
            StoreInfo store = storeService.getStoreById(BeanUtil.getPropString(data, "storeId"), devCode);
            storeSn = store.getSn();
            terminalSn = BeanUtil.getPropString(contextParam, "terminalSn");
            snType = TradeExtConfigCreateRequest.SN_TYPE_TERMINAL;
        }
        //富友T9Pos解绑又IOT组触发
        List<String> fyT9PosList = factory.getFyT9PosList();
        if (fyT9PosList.contains(vendorAppAppid)) {
            return ContractResponse.builder().code(200).message("富友T9终端不需要绑定").build();
        }
        MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(task.getMerchant_sn(), ProviderEnum.PROVIDER_FUYOU.getValue());
        if (Objects.isNull(acquirerParams)) {
            return ContractResponse.builder().code(460).message("商户未进件成功,终端绑定失败").build();
        }
        ContractResponse response = addTerminalToFuyou(task.getMerchant_sn(), acquirerParams.getPay_merchant_id(), vendorAppAppid, terminalSn);
        if (response.isSuccess()) {
            Map responseParam = response.getResponseParam();
            if ("0000".equals(BeanUtil.getPropString(responseParam, "ret_code"))) {
                String providerTerminalId = BeanUtil.getPropString(responseParam, "fy_term_id");
                ProviderTerminal providerTerminal = new ProviderTerminal();
                providerTerminal.setProvider_terminal_id(providerTerminalId);
                List<ProviderTerminal> providerTerminalList = terminalMapper.selectByCondition(providerTerminal);
                if (providerTerminalList == null || providerTerminalList.isEmpty()) {
                    //新增 provider_terminal表
                    ProviderTerminal terminal = new ProviderTerminal();
                    terminal.setMerchant_sn(task.getMerchant_sn());
                    terminal.setProvider(ProviderEnum.PROVIDER_FUYOU.getValue());
                    terminal.setProvider_terminal_id(providerTerminalId);
                    terminal.setAcquirer_merchant_id(acquirerParams.getPay_merchant_id());
                    terminal.setTerminal_appid(vendorAppAppid);
                    terminal.setTerminal_sn(terminalSn);
                    terminal.setStore_sn(storeSn);
                    terminalMapper.insertSelective(terminal);
                }
                //调用交易接
                createOrUpdateTradeExtConfig(tradeSn, providerTerminalId, snType);

            }
        }
        return response;
    }

    private void createOrUpdateTradeExtConfig(String sn, String providerTerminalId, Integer type) {
        //查询是否存在
        TradeExtConfigQueryRequest queryRequest = new TradeExtConfigQueryRequest();
        queryRequest.setProvider(ProviderEnum.PROVIDER_FUYOU.getValue());
        queryRequest.setSn(sn);
        queryRequest.setSnType(type);
        TradeExtConfigQueryResponse queryResponse = tradeConfigService.queryTradeExtConfig(queryRequest);
        TradeExtConfigContentModel content = new TradeExtConfigContentModel();
        content.setTermId(providerTerminalId);
        if (Objects.isNull(queryResponse)) {
            //调用交易新增接口
            TradeExtConfigCreateRequest request = new TradeExtConfigCreateRequest();
            request.setSn(sn);
            //0：商户、1：门店、2：终端
            request.setProvider(ProviderEnum.PROVIDER_FUYOU.getValue());
            request.setSnType(type);
            request.setContent(content);
            tradeConfigService.createTradeExtConfig(request);
        } else {
            //调用交易修改接口
            TradeExtConfigUpdateRequest tradeExtConfigUpdateRequest = new TradeExtConfigUpdateRequest();
            tradeExtConfigUpdateRequest.setProvider(ProviderEnum.PROVIDER_FUYOU.getValue());
            tradeExtConfigUpdateRequest.setSn(sn);
            tradeExtConfigUpdateRequest.setSnType(type);
            tradeExtConfigUpdateRequest.setContent(content);
            tradeConfigService.updateTradeExtConfig(tradeExtConfigUpdateRequest);
        }
    }


    private ContractResponse addTerminalToFuyou(String merchantSn, String acquirerMerchantId, String vendorAppAppId, String terminalSn) {
        Map<String, Object> merchant = merchantService.getMerchantByMerchantSn(merchantSn);
        FuyouParam fuyouParam = contractParamsBiz.buildContractParams("fuyou", FuyouParam.class);
        TerminalOperateRequest request = buildTerminalOperateRequest(merchant, acquirerMerchantId, vendorAppAppId);
        if (StringUtil.empty(terminalSn)) {
            request.setTm_serial_no(request.getMchnt_cd() + merchantSn);
        } else {
            request.setTm_serial_no(request.getMchnt_cd() + terminalSn);
        }
        request.setHandle_type("00");
        request.setTm_device_state("00");
        return fuyouService.terminalOperate(request, fuyouParam);
    }


    /**
     * 终端/子商户号绑定
     *
     * @param termInfoDTO
     * @param payWay
     * @param terminalSn
     * @return
     */
    @Override
    public ContractResponse boundTerminal(AddTermInfoDTO termInfoDTO, int payWay, String terminalSn) {
        return ContractResponse.builder().code(200).message("绑定成功").build();
    }

    private TerminalOperateRequest buildTerminalOperateRequest(Map<String, Object> merchant, String acquirerMerchantId, String venderAppid) {
        TerminalOperateRequest request = new TerminalOperateRequest();
        request.setMerchant_sn(WosaiMapUtils.getString(merchant, Merchant.SN));
        if (StringUtil.empty(venderAppid)) {
            request.setTm_mode("JH-XCX");
        } else {
            Map<String, String> terminalMap = applicationApolloConfig.getFuyouTerminalMode();
            request.setTm_mode(terminalMap.getOrDefault(venderAppid, "JH-XCX"));
        }
        request.setMchnt_cd(acquirerMerchantId);
        String province = BeanUtil.getPropString(merchant, Merchant.PROVINCE);
        String city = BeanUtil.getPropString(merchant, Merchant.CITY);
        String district = BeanUtil.getPropString(merchant, Merchant.DISTRICT);
        Map extra = WosaiMapUtils.getMap(merchant, Merchant.EXTRA);
        if (extra == null) {
            request.setTm_contact_addr(province + "-" + city + "-" + district + "-" + MapUtils.getString(merchant, Merchant.STREET_ADDRESS));
        }
        if (StringUtils.isEmpty(BeanUtil.getPropString(extra, "offsetInfo"))) {
            request.setTm_contact_addr(province + "-" + city + "-" + district + "-" + MapUtils.getString(merchant, Merchant.STREET_ADDRESS));
        }
        request.setLongitude("E" + MapUtils.getString(merchant, Merchant.LONGITUDE));
        request.setLatitude("S" + MapUtils.getString(merchant, Merchant.LATITUDE));
        Map offsetInfo = JSONObject.parseObject(BeanUtil.getPropString(extra, "offsetInfo"));
        String address = BeanUtil.getPropString(offsetInfo, "complete_address");
        if (!StringUtils.isEmpty(address) && address.length() >= 6) {
            request.setLongitude("E" + MapUtils.getString(offsetInfo, Merchant.LONGITUDE));
            request.setLatitude("S" + MapUtils.getString(offsetInfo, Merchant.LATITUDE));
            request.setTm_contact_addr(address);
        }
        if (request.getLongitude() != null) {
            request.setLongitude(request.getLongitude().substring(0, request.getLongitude().length() - 2));
        }
        if (request.getLatitude() != null) {
            request.setLatitude(request.getLatitude().substring(0, request.getLatitude().length() - 2));
        }
        return request;
    }

    /**
     * 创建终端绑定
     *
     * @param merchantSn
     * @param provider
     */
    @Override
    public void doCreateProviderTerminal(String merchantSn, Integer provider) {
        MerchantInfo merchant = centerMerchantService.getMerchantBySn(merchantSn, devCode);
        produceInsertTerminalTaskByRule(null, merchant);
    }


    @Override
    public List getFeeRate(String merchantId) {
        if (StringUtil.empty(merchantId)) {
            return null;
        }
        List<Map> merchantConfigs = tradeConfigService.getAnalyzedMerchantConfigs(merchantId);
        Map weixinConfig = null;
        Map alipayConfig = null;
        Map jdConfig = null;
        Map qqConfig = null;
        Map lklWalletConfig = null;
        Map unionConfig = null;
        Map bestPayConfig = null;
        for (Map merchantConfig : merchantConfigs) {
            int payway = BeanUtil.getPropInt(merchantConfig, MerchantConfig.PAYWAY);
            if (payway == PaywayEnum.ALIPAY.getValue()) {
                alipayConfig = merchantConfig;
            } else if (payway == PaywayEnum.WEIXIN.getValue()) {
                weixinConfig = merchantConfig;
            } else if (payway == PaywayEnum.JD_WALLET.getValue()) {
                jdConfig = merchantConfig;
            } else if (payway == PaywayEnum.QQ_WALLET.getValue()) {
                qqConfig = merchantConfig;
            } else if (payway == PaywayEnum.LAKALA_WALLET.getValue()) {
                lklWalletConfig = merchantConfig;
            } else if (payway == PaywayEnum.UNIONPAY.getValue()) { //银联二维码支付
                unionConfig = merchantConfig;
            } else if (payway == PaywayEnum.BESTPAY.getValue()) {
                bestPayConfig = merchantConfig;
            }
        }

        if (weixinConfig == null || alipayConfig == null) {
            throw new ContextParamException("商户的支付宝或者微信交易配置merchant_config未配置");
        }

        String alipayFeerate = getLadderFee(alipayConfig, MerchantConfig.WAP_FEE_RATE);
        String weixinFeerate = getLadderFee(weixinConfig, MerchantConfig.WAP_FEE_RATE);
        String jdFeerate = getLadderFee(jdConfig, MerchantConfig.WAP_FEE_RATE);
        String qqFeerate = getLadderFee(qqConfig, MerchantConfig.WAP_FEE_RATE);
        String lklWalletFeeRate = getLadderFee(lklWalletConfig, MerchantConfig.B2C_FEE_RATE);
        String unionFeeRate = getLadderFee(unionConfig, MerchantConfig.WAP_FEE_RATE);
        String bestPayFeeRate = getLadderFee(bestPayConfig, MerchantConfig.B2C_FEE_RATE);
        List list = Arrays.asList(
                CollectionUtil.hashMap(
                        LakalaBusinessFileds.WECHAT_TYPE, LakalaConstant.WECHAT_PAY_FEE,
                        LakalaBusinessFileds.WECHAT_RATE, formatFeerate(weixinFeerate)
                ), CollectionUtil.hashMap(
                        LakalaBusinessFileds.WECHAT_TYPE, LakalaConstant.ALIPAY_WALLET_FEE,
                        LakalaBusinessFileds.WECHAT_RATE, formatFeerate(alipayFeerate)
                ), CollectionUtil.hashMap(
                        LakalaBusinessFileds.WECHAT_TYPE, LakalaConstant.JINGDONG_PURCHASE_FEE,
                        LakalaBusinessFileds.WECHAT_RATE, formatFeerate(jdFeerate)
                ), CollectionUtil.hashMap(
                        LakalaBusinessFileds.WECHAT_TYPE, LakalaConstant.QQ_PURCHASE_FEE,
                        LakalaBusinessFileds.WECHAT_RATE, formatFeerate(qqFeerate)
                ), CollectionUtil.hashMap(
                        LakalaBusinessFileds.WECHAT_TYPE, LakalaConstant.LAKALA_WALLET_FEE,
                        LakalaBusinessFileds.WECHAT_RATE, formatFeerate(lklWalletFeeRate)
                ), CollectionUtil.hashMap(
                        LakalaBusinessFileds.WECHAT_TYPE, LakalaConstant.UNIONPAY_WALLET_DEBIT_FEE,
                        LakalaBusinessFileds.WECHAT_RATE, formatFeerate(unionFeeRate)
                ), CollectionUtil.hashMap(
                        LakalaBusinessFileds.WECHAT_TYPE, LakalaConstant.UNIONPAY_WALLET_CREDIT_FEE,
                        LakalaBusinessFileds.WECHAT_RATE, formatFeerate(unionFeeRate)
                ), CollectionUtil.hashMap(LakalaBusinessFileds.WECHAT_TYPE, LakalaConstant.BESTPAY_PURCHASE_FEE,
                        LakalaBusinessFileds.WECHAT_RATE, formatFeerate(bestPayFeeRate)
                ));
        return list;
    }


    protected String getLadderFee(Map merchantConfig, String feeKey) {
        if (isLadderFeeRate(merchantConfig)) {
            Object configs = BeanUtil.getProperty(merchantConfig, MerchantConfig.LADDER_FEE_RATES);
            if (configs != null && configs instanceof List) {
                StringBuilder fee = new StringBuilder();
                for (Map config : (List<Map>) configs) {
                    fee.append(BeanUtil.getPropString(config, "min")).append("-")
                            .append(BeanUtil.getPropString(config, "max", "")).append("_")
                            .append(new BigDecimal(BeanUtil.getPropString(config, feeKey)).multiply(new BigDecimal("100")).intValue()).append(",");
                }
                if (!StringUtil.empty(fee.toString())) {
                    return fee.substring(0, fee.length() - 1);
                }
            }
        }
        return BeanUtil.getPropString(merchantConfig, feeKey);
    }

    /**
     * 拉卡拉的费率表示为千分位
     * 0.38 ---> 0.0038
     * 0.6  ---> 0.006 这么转换
     *
     * @param feerate
     * @return
     */
    protected String formatFeerate(String feerate) {
        if (feerate.contains("-")) {
            return feerate;
        }
        String str = String.format("%.4f", Double.valueOf(feerate) / 100);
        char lastChar = str.charAt(str.length() - 1);
        if (lastChar == '0') {
            str = str.substring(0, str.length() - 1);
        }
        return str;
    }

    private MerchantInfo saveAcceptanceCertificate(String merchantSn, com.wosai.upay.bank.model.MerchantBankAccount bankAccount) {
        MerchantInfo merchant = centerMerchantService.getMerchantBySn(merchantSn, null);
        UpdateMerchantReq updateMerchantReq = new UpdateMerchantReq();
        Map extra = merchant.getExtra();
        if (CollectionUtils.isEmpty(extra)) {
            extra = new HashMap();
        }
        Map<String, String> fuyou_public_account = new HashMap<>();
        fuyou_public_account.put(MerchantBankAccount.OPENING_NUMBER, bankAccount.getOpening_number());
        fuyou_public_account.put(MerchantBankAccount.HOLDER, bankAccount.getHolder());
        fuyou_public_account.put(MerchantBankAccount.NUMBER, bankAccount.getNumber());
        fuyou_public_account.put(MerchantBankAccount.BANK_NAME, bankAccount.getBank_name());
        extra.put(FUYOU_PUBLIC_BANK_ACCOUNT, fuyou_public_account);
        extra.put(CERTFICATE, BeanUtil.getPropString(bankAccount, MerchantBankAccount.BANK_CARD_IMAGE));
        updateMerchantReq.setId(BeanUtil.getPropString(merchant, com.wosai.upay.common.dao.DaoConstants.ID));
        updateMerchantReq.setExtra(extra);
        centerMerchantService.updateMerchant(updateMerchantReq, null);
        return centerMerchantService.getMerchantBySn(merchantSn, null);
    }


    @Override
    public ContractResponse doProcessMicroUpgradeTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask sub) {
        FuyouParam fuyouParam = buildParam(contractChannel, sub, FuyouParam.class);
        Map contextParam = JSON.parseObject(contractTask.getEvent_context(), Map.class);
        ContractRule contractRule = ruleContext.getContractRule(sub.getContract_rule());
        ContractResponse response = null;
        if (contractRule.getType() == 1) {
            response = fuyouService.microUpgradeContractToAcquirer(contextParam, fuyouParam);

        } else if (contractRule.getType() == 2) {
            //富友商户号
            final String fyMcNo = getFYMcNoByResponse(contractTask.getId());
            response = fuyouService.microUpgradeApplyElectronicAgreement(contractTask.getMerchant_sn(), fuyouParam,fyMcNo);
        } else if (contractRule.getType() == 3) {
            //富友商户号
            final String fyMcNo = getFYMcNoByResponse(contractTask.getId());
            ContractSubTask subTask = contractSubTaskMapper.selectByPrimaryKey(sub.getSchedule_dep_task_id());
            Map<String, Object> singResponse = MapUtils.getMap(subTask.getResponseBody(), "responseParam");
            SignElectronicAgreementRequest request = new SignElectronicAgreementRequest();
            request.setMerchant_sn(contractTask.getMerchant_sn());
            request.setMchnt_cd(fyMcNo);
            request.setContract_no(BeanUtil.getPropString(singResponse, "contract_no"));
            request.setVerify_no(BeanUtil.getPropString(singResponse, "verify_no"));
            response = fuyouService.microUpgradesignElectronicAgreement(request, fuyouParam);
        } else if (contractRule.getType() == 4) {
            //富友商户号
            final String fyMcNo = getFYMcNoByResponse(contractTask.getId());
            response = fuyouService.microUpgradeUploadFilesZip(contextParam, fuyouParam,fyMcNo);
        } else if (contractRule.getType() == 5) {
            final String fyMcNo = getFYMcNoByResponse(contractTask.getId());
            response = fuyouService.microUpgradeConfirmUploadFiles(contractTask.getMerchant_sn(), fuyouParam,fyMcNo);
            MerchantProviderParamsExample dto = new MerchantProviderParamsExample();
            dto.createCriteria().andMerchant_snEqualTo(contractTask.getMerchant_sn()).andPaywayEqualTo(PaywayEnum.ACQUIRER.getValue()).andProviderEqualTo(ProviderEnum.PROVIDER_FUYOU.getValue()).andDeletedEqualTo(false);
        } else if (contractRule.getType() == 6) {
            final String fyMcNo = getFYMcNoByResponse(contractTask.getId());
            response = fuyouService.microUpgradeQueryMchId(contractTask.getMerchant_sn(), PaywayEnum.WEIXIN.getValue(), fuyouParam,fyMcNo);
            Map<String, Object> tradeParam = response.getTradeParam();
            if (response.isSuccess()) {
                Map respParam = response.getResponseParam();
                String payMerchantId = BeanUtil.getPropString(respParam, "sub_mch_id");
                tradeParam.put(MerchantProviderTradeParams.PROVIDER_MERCHANT_ID,fyMcNo);
                tradeParam.put(MerchantProviderTradeParams.WEIXIN_MERCHANT_ID, payMerchantId);
                response.setTradeParam(tradeParam);
            } else {
                response.setTradeParam(tradeParam);
            }
        } else if (contractRule.getType() == 7) {
            final String fyMcNo = getFYMcNoByResponse(contractTask.getId());
            response = fuyouService.microUpgradeQueryMchId(contractTask.getMerchant_sn(), PaywayEnum.ALIPAY.getValue(), fuyouParam,fyMcNo);
            Map<String, Object> tradeParam = response.getTradeParam();
            if (response.isSuccess()) {
                Map respParam = response.getResponseParam();
                String payMerchantId = BeanUtil.getPropString(respParam, "sub_mch_id");
                tradeParam.put(MerchantProviderTradeParams.PROVIDER_MERCHANT_ID, fyMcNo);
                tradeParam.put(MerchantProviderTradeParams.WEIXIN_MERCHANT_ID, payMerchantId);
                response.setTradeParam(tradeParam);
            } else {
                response.setTradeParam(tradeParam);
            }
        }else if(8 == contractRule.getType()) {
            //是否申请开通了云闪付二维码
            String modifyNo = sub.getContract_id();
            final String fyMcNo = getFYMcNoByResponse(contractTask.getId());
            if(StringUtils.isEmpty(modifyNo)) {
                response = fuyouService.microUpgradeMchntOpenUpayQr(sub.getMerchant_sn(), contextParam,fyMcNo);
                if(response.isSuccess()){
                    final MchntOpenUpayQrResponse mchntOpenUpayQrResponse = JSONObject.parseObject(JSONObject.toJSONString(response.getResponseParam()), MchntOpenUpayQrResponse.class);
                    modifyNo = mchntOpenUpayQrResponse.getModifyNo();
                    //存储相关必要信息,用于业务方做业务判断
                    Map<String, Object> tradeParams = Maps.newHashMap();
                    tradeParams.put("contractId", modifyNo);
                    response.setTradeParam(tradeParams);
                }
            }else {//申请成功后->5.1 业务审核状态查询接口-->7.2 商户渠道子商户号查询接口
                final String merchantSn = contractTask.getMerchant_sn();
                //业务审核状态查询接口
                response = fuyouService.microUpgradeMchntQueryTZ(merchantSn, modifyNo, MODIFY_TP_YL,fyMcNo);
                //获取当前sub_task的request_body,把这个固定放在response.setRequestParam中为了确保在更新云闪付商户的信息是不会改变mchnt_sub_tp
                response.setRequestParam(sub.getRequestBody());
                if(!response.isSuccess()) {
                    return response;
                }
                // 解析查询响应
                MchntQueryTZResponse mchntQueryTZResponse = JSONObject.parseObject(JSONObject.toJSONString(response.getResponseParam()), MchntQueryTZResponse.class);
                String modifyStatus = mchntQueryTZResponse.getModifyStatus();
                final String modifyStatusDesc = mchntQueryTZResponse.getModifyStatusDesc();
                // 拒绝状态处理
                if (Objects.equals(modifyStatus, REJECTED)) {
                    return response.setCode(Constant.RESULT_CODE_BIZ_EXCEPTION).setMessage(StringUtils.isEmpty(modifyStatusDesc) ? "审核失败" :modifyStatusDesc);
                }
                // 审核中
                if (!Objects.equals(modifyStatus, COMPLETED)) {
                    return response.setCode(Constant.RESULT_CODE_SYSTEM_EXCEPTION).setMessage(StringUtils.isEmpty(modifyStatusDesc) ? "审核中" :modifyStatusDesc);
                }
                //审核通过 --> 商户渠道子商户号查询接口
                response = fuyouService.microUpgradeQueryMchId(merchantSn, PaywayEnum.UNIONPAY.getValue(), fuyouParam,fyMcNo);
                //获取当前sub_task的request_body,把这个固定放在response.setRequestParam中为了确保在更新云闪付商户的信息是不会改变mchnt_sub_tp
                response.setRequestParam(sub.getRequestBody());
                Map<String, Object> tradeParam = response.getTradeParam();
                if (!response.isSuccess()) {
                    return response;
                }
                //富友子商户号查询接口返回
                Map respParam = response.getResponseParam();
                String payMerchantId = BeanUtil.getPropString(respParam, "sub_mch_id");
                tradeParam.put(MerchantProviderTradeParams.PROVIDER_MERCHANT_ID, fyMcNo);
                tradeParam.put(MerchantProviderTradeParams.UNIONOPEN_MERCHANT_ID, payMerchantId);
                response.setTradeParam(tradeParam);
            }
        }
        return response;

    }


    /**
     * 通过入网任务返回获取富友商户号
     * @param pTaskId
     * @return
     */
    public String getFYMcNoByResponse(Long pTaskId) {
        final List<ContractSubTaskDO> subTaskDOS = contractSubTaskDAO.listByPTaskId(pTaskId);
        final Optional<ContractSubTaskDO> first = subTaskDOS.stream().filter(r -> PaywayEnum.ACQUIRER.getValue().equals(r.getPayway())
                && Objects.equals(r.getStatus(), ContractSubTaskProcessStatusEnum.PROCESS_SUCCESS.getValue())
                && Objects.equals(r.getTaskType(), ContractSubTaskTypeEnum.MERCHANT_OPENING.getValue())
                && Objects.equals(r.getContractRule(), "fuyou")
        ).findFirst();
       return first.map(sub-> {
                    final String requestBody = sub.getRequestBody();
                   return BeanUtil.getNestedProperty(JSON.parseObject(requestBody, Map.class),"responseParam.fy_mchnt_cd").toString();
                })
                .orElseThrow(() -> new ContractBizException("没有找到对应的入网任务"));
    }

}
