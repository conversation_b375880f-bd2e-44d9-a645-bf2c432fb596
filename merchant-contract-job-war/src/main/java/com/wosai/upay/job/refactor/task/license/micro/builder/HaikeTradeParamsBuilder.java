package com.wosai.upay.job.refactor.task.license.micro.builder;

import com.wosai.upay.job.model.MerchantAcquireInfoBO;
import com.wosai.upay.job.refactor.biz.acquirer.haike.HaikeAcquirerFacade;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleMainTaskDO;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleSubTaskDO;
import com.wosai.upay.job.refactor.task.license.BusinessLicenceCertificationV3Task;
import com.wosai.upay.job.refactor.task.license.micro.builder.context.TradeParamsBuilderContext;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 海科交易参数构建器
 * 
 * <AUTHOR>
 */
@Component
public class HaikeTradeParamsBuilder extends AbstractTradeParamsBuilder {
    
    public static final String ACQUIRER_TYPE = "HAIKE";
    
    @Autowired
    private HaikeAcquirerFacade haikeAcquirerFacade;
    
    @Autowired
    private BusinessLicenceCertificationV3Task businessLicenceCertificationV3Task;
    

    
    @Override
    protected String getAcquirerType() {
        return ACQUIRER_TYPE;
    }
    
    @Override
    protected MerchantAcquireInfoBO getMerchantAcquireInfo(TradeParamsBuilderContext context) {
        return haikeAcquirerFacade.getAcquireInfoFromContractSubTask(context.getContractTaskId());
    }
    
    @Override
    protected TradeParamsBuilderContext buildContext(InternalScheduleMainTaskDO mainTaskDO,
                                                     InternalScheduleSubTaskDO subTaskDO) {
        TradeParamsBuilderContext context = super.buildContext(mainTaskDO, subTaskDO);
        
        // 构建合同参数上下文（用于微信认证）
        context.setContractParamContext(businessLicenceCertificationV3Task.buildContractContext(mainTaskDO));
        
        return context;
    }
}
