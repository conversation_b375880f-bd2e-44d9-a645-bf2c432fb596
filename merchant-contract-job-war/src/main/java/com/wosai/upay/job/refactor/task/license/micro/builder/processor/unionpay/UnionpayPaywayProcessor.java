package com.wosai.upay.job.refactor.task.license.micro.builder.processor.unionpay;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.job.biz.WechatAuthBiz;
import com.wosai.upay.job.model.MerchantAcquireInfoBO;
import com.wosai.upay.job.refactor.model.bo.LklOpenUnionPayTradeParamBO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.task.license.micro.builder.context.TradeParamsBuilderContext;
import com.wosai.upay.job.refactor.task.license.micro.builder.processor.PaywayProcessor;

import java.util.HashMap;

/**
 * 银联支付方式处理器
 *
 * <AUTHOR>
 */
public class UnionpayPaywayProcessor implements PaywayProcessor {

    private final String acquirerType;

    private WechatAuthBiz wechatAuthBiz;

    public UnionpayPaywayProcessor(String acquirerType) {
        this.acquirerType = acquirerType;
    }

    @Override
    public void processParam(MerchantProviderParamsDO newParam,
                             MerchantProviderParamsDO oldParam,
                             TradeParamsBuilderContext context) {
        
        MerchantAcquireInfoBO merchantAcquireInfo = (MerchantAcquireInfoBO) context.getMerchantAcquireInfo();
        
        // 设置银联商户号
        newParam.setPayMerchantId(merchantAcquireInfo.getUnionNo());
        
        // 根据不同收单机构设置特定参数
        switch (acquirerType) {
            case "LKLV3":
                processLklV3UnionPay(newParam, merchantAcquireInfo);
                break;
            case "FUYOU":
                processFuyouUnionPay(newParam, merchantAcquireInfo, context);
                break;
            case "HAIKE":
                processHaikeUnionPay(newParam, merchantAcquireInfo);
                break;
            default:
                // 默认处理
                break;
        }
    }
    
    /**
     * 处理拉卡拉V3银联参数
     */
    private void processLklV3UnionPay(MerchantProviderParamsDO newParam, MerchantAcquireInfoBO merchantAcquireInfo) {
        newParam.setProviderMerchantId(merchantAcquireInfo.getUnionNo());
        
        LklOpenUnionPayTradeParamBO lklOpenUnionPayTradeParamBO = new LklOpenUnionPayTradeParamBO();
        lklOpenUnionPayTradeParamBO.setProviderMerchantId(merchantAcquireInfo.getUnionNo());
        lklOpenUnionPayTradeParamBO.setTermId(merchantAcquireInfo.getLklTermNo());
        newParam.setExtra(JSON.toJSONString(lklOpenUnionPayTradeParamBO));
    }
    
    /**
     * 处理富友银联参数
     */
    private void processFuyouUnionPay(MerchantProviderParamsDO newParam, 
                                    MerchantAcquireInfoBO merchantAcquireInfo,
                                    TradeParamsBuilderContext context) {
        newParam.setProviderMerchantId(merchantAcquireInfo.getAcquireMerchantId());
        newParam.setMerchantName(wechatAuthBiz.getWechatAuthMerchantName(context.getContractParamContext()));
    }
    
    /**
     * 处理海科银联参数
     */
    private void processHaikeUnionPay(MerchantProviderParamsDO newParam, MerchantAcquireInfoBO merchantAcquireInfo) {
        newParam.setProviderMerchantId(merchantAcquireInfo.getAcquireMerchantId());
        newParam.setMerchantName(merchantAcquireInfo.getHaikeUnionName());
        
        HashMap<String, Object> extra = new HashMap<>();
        extra.put("tradeParams", CollectionUtil.hashMap("bankMerchNo", merchantAcquireInfo.getUnionNo()));
        newParam.setExtra(JSON.toJSONString(extra));
    }
    
    @Override
    public Integer getSupportedPayway() {
        return PaywayEnum.UNIONPAY.getValue();
    }
    
    @Override
    public String getSupportedAcquirerType() {
        return acquirerType;
    }

    // Setter method for dependency injection
    public void setWechatAuthBiz(WechatAuthBiz wechatAuthBiz) {
        this.wechatAuthBiz = wechatAuthBiz;
    }
}
