package com.wosai.upay.job.refactor.task.license.micro.builder.processor.weixin;

import com.alibaba.fastjson.JSONObject;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.job.biz.WechatAuthBiz;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.MerchantAcquireInfoBO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.task.license.micro.builder.context.TradeParamsBuilderContext;
import com.wosai.upay.job.refactor.task.license.micro.builder.processor.PaywayProcessor;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 微信支付方式处理器
 * 
 * <AUTHOR>
 */
public class WeixinPaywayProcessor implements PaywayProcessor {
    
    private final String acquirerType;

    private WechatAuthBiz wechatAuthBiz;
    
    public static final String AUTH_TIME = "auth_time";
    
    public WeixinPaywayProcessor(String acquirerType) {
        this.acquirerType = acquirerType;
    }
    
    @Override
    public void processParam(MerchantProviderParamsDO newParam,
                             MerchantProviderParamsDO oldParam,
                             TradeParamsBuilderContext context) {
        
        MerchantAcquireInfoBO merchantAcquireInfo = (MerchantAcquireInfoBO) context.getMerchantAcquireInfo();
        
        // 设置微信商户号
        newParam.setPayMerchantId(merchantAcquireInfo.getWxNo());
        
        // 获取微信认证信息
        WechatAuthBiz.WechatAuthNameAndSettId merchantNameAndSettlementId = 
                wechatAuthBiz.getMerchantNameAndSettlementId(context.getContractParamContext());
        
        // 设置微信相关参数
        newParam.setWeixinSubAppid(null);
        newParam.setWeixinSubMiniAppid(null);
        newParam.setWxSettlementId(merchantNameAndSettlementId.getSettlementId());
        newParam.setMerchantName(merchantNameAndSettlementId.getMerchantName());
        newParam.setParamsConfigStatus(MerchantProviderParams.PARAMS_CONFIG_STATUS_PRE);
        
        // 设置认证时间
        newParam.setExtra(JSONObject.toJSONString(
                CollectionUtil.hashMap(AUTH_TIME, context.getWxAuthTime())));
        
        // 根据不同收单机构设置特定参数
        switch (acquirerType) {
            case "LKLV3":
                newParam.setProviderMerchantId(merchantAcquireInfo.getUnionNo());
                break;
            case "FUYOU":
            case "HAIKE":
                newParam.setProviderMerchantId(merchantAcquireInfo.getAcquireMerchantId());
                break;
            default:
                // 默认处理
                break;
        }
    }
    
    @Override
    public Integer getSupportedPayway() {
        return PaywayEnum.WEIXIN.getValue();
    }
    
    @Override
    public String getSupportedAcquirerType() {
        return acquirerType;
    }

    // Setter method for dependency injection
    public void setWechatAuthBiz(WechatAuthBiz wechatAuthBiz) {
        this.wechatAuthBiz = wechatAuthBiz;
    }
}
