package com.wosai.upay.job.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.BusinessLicenseTypeEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.PayMchAuthStatusEnum;
import com.shouqianba.cua.enums.status.UseStatusEnum;
import com.shouqianba.cua.model.http.LogParamsDto;
import com.wosai.common.exception.CommonDataObjectNotExistsException;
import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.cua.brand.business.api.enums.PaymentModeEnum;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.databus.event.crmdatabus.merchantBusinessOpen.appInfo.DataBusAppInfo;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.service.MerchantBusinessLicenseService;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.bank.info.api.service.DistrictsService;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantBusinessLicence;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.Constants.TradeConstants;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.adapter.apollo.MemoApolloConfig;
import com.wosai.upay.job.biz.*;
import com.wosai.upay.job.biz.acquirer.ComposeAcquirerBiz;
import com.wosai.upay.job.biz.directparams.DirectParamsBizFactory;
import com.wosai.upay.job.biz.sceneManage.ErrorCodeManageBiz;
import com.wosai.upay.job.constant.CommonConstants;
import com.wosai.upay.job.enume.PlatformEnum;
import com.wosai.upay.job.externalservice.brand.BrandBusinessClient;
import com.wosai.upay.job.externalservice.brand.model.BrandDetailInfoQueryResp;
import com.wosai.upay.job.externalservice.brand.model.BrandMerchantInfoQueryResp;
import com.wosai.upay.job.handlers.CommonEventHandler;
import com.wosai.upay.job.mapper.*;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsCustom;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExt;
import com.wosai.upay.job.model.application.CommonResult;
import com.wosai.upay.job.model.directparams.*;
import com.wosai.upay.job.model.dto.*;
import com.wosai.upay.job.model.subBizParams.SubBizConfig;
import com.wosai.upay.job.model.subBizParams.SubBizParams;
import com.wosai.upay.job.model.subBizParams.SubBizParamsExample;
import com.wosai.upay.job.model.wechatAuth.WxMchInfo;
import com.wosai.upay.job.providers.BasicProvider;
import com.wosai.upay.job.providers.ProviderFactory;
import com.wosai.upay.job.refactor.biz.params.MerchantTradeParamsBiz;
import com.wosai.upay.job.refactor.dao.McAcquirerDAO;
import com.wosai.upay.job.refactor.dao.McContractRuleDAO;
import com.wosai.upay.job.refactor.dao.McProviderDAO;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.model.converter.MerchantProviderParamsConverter;
import com.wosai.upay.job.refactor.model.entity.McAcquirerDO;
import com.wosai.upay.job.refactor.model.entity.McContractRuleDO;
import com.wosai.upay.job.refactor.model.entity.McProviderDO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.util.*;
import com.wosai.upay.merchant.contract.constant.LakalaWanmaBusinessFileds;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.exception.ContractSysException;
import com.wosai.upay.merchant.contract.model.WeixinAppidConfig;
import com.wosai.upay.merchant.contract.model.WeixinConfig;
import com.wosai.upay.merchant.contract.model.weixin.MchInfo;
import com.wosai.upay.merchant.contract.model.weixin.SubdevConfigResp;
import com.wosai.upay.merchant.contract.service.LklV3Service;
import com.wosai.upay.merchant.contract.service.PsbcService;
import com.wosai.upay.merchant.contract.service.TongLianV2Service;
import com.wosai.web.api.ListResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import vo.ApiRequestParam;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

import static com.wosai.upay.job.biz.bankDirect.HxbImportBiz.TRADEPARAMS;

/**
 * <AUTHOR>
 * @date 2019-07-22
 */
@Slf4j
@Service
@AutoJsonRpcServiceImpl
public class MerchantProviderParamsServiceImpl implements MerchantProviderParamsService {

    public static final Logger logger = LoggerFactory.getLogger(MerchantProviderParamsServiceImpl.class);

    @Autowired
    private TradeConfigService tradeConfigService;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private DistrictsService districtsService;
    @Autowired
    private ChangeTradeParamsBiz tradeParamsBiz;

    @Autowired
    private MerchantProviderParamsBiz paramsBiz;

    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;
    @Autowired
    private MerchantProviderParamsDAO merchantProviderParamsDAO;

    @Autowired
    private McAcquirerDAO mcAcquirerDAO;

    @Autowired
    private RuleContext ruleContext;

    @Autowired
    private ProviderFactory providerFactory;

    @Autowired
    private BusinessCategory businessCategory;

    @Autowired
    MerchantBusinessLicenseService merchantBusinessLicenseService;
    @Autowired
    SupportService supportService;

    @Autowired
    MerchantConfigParamsBiz merchantConfigParamsBiz;

    @Autowired
    MerchantProviderParamsExtMapper merchantProviderParamsExtMapper;

    @Autowired
    private SyncBiz syncBiz;
    @Autowired
    BusinessLogBiz businessLogBiz;

    @Autowired
    PsbcService psbcService;
    @Autowired
    SubBizParamsMapper subBizParamsMapper;

    @Value("${lkl.channelno}")
    private String lklChannelNo;
    @Value("${lkl.v3.channelno}")
    private String lklv3ChannelNo;

    @Value("${indirect-pay.dev_code}")
    private String devCode;

    @Value("${fy_pso_dev_code}")
    public String fyPsoDevCode;

    @Autowired
    ContractStatusMapper contractStatusMapper;

    @Autowired
    MemoApolloConfig memoApolloConfig;

    @Autowired
    private ErrorCodeManageBiz errorCodeManageBiz;

    @Autowired
    private ComposeAcquirerBiz composeAcquirerBiz;

    @Lazy
    @Autowired
    CommonEventHandler commonEventHandler;

    @Autowired
    SubBizParamsBiz subBizParamsBiz;

    @Autowired
    private WxSettlementIdChangeBiz wxSettlementIdChangeBiz;

    @Autowired
    OfflineMultiTradeMapper offlineMultiTradeMapper;

    @Autowired
    private McProviderDAO mcProviderDAO;

    @Autowired
    private WechatAuthBiz wechatAuthBiz;

    @Autowired
    private AlipayAuthBiz alipayAuthBiz;
    @Autowired
    private LklV3Service lklV3Service;

    @Autowired
    private McContractRuleDAO mcContractRuleDAO;

    @Resource
    private ApplicationApolloConfig applicationApolloConfig;
    @Autowired
    private PaymentModeChangeBiz paymentModeChangeBiz;
    @Autowired
    private BrandBusinessClient brandBusinessClient;

    @Resource
    private MerchantTradeParamsBiz merchantTradeParamsBiz;

    //高校食堂
    private static final String COLLEGE_CHANNEL = "36TB4213341";

    //微信高校食堂是否使用特殊渠道
    public static final String USE_SPECIAL_CHANNEL = "use_special_channel";

    @Value("${b2b.appid}")
    private String b2bAppId;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addAlipayV2DirectParams(AlipayV2DirectParams params) {
        return addAlipayV2DirectParamsWithLog(params, new LogParamsDto().setSceneTemplateCode(CommonConstants.BUSINESS_LOG_NO_LOG));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addAlipayV2DirectParamsWithLog(AlipayV2DirectParams params, LogParamsDto dto) {
        if (Objects.isNull(dto) || StringUtils.isEmpty(dto.getSceneTemplateCode())) {
            throw new ContractBizException(CommonConstants.EXCEPTION_ERROR_MSG);
        }
        DirectParamsBizFactory.getDirectParamsBiz(PaywayEnum.ALIPAY.getValue()).addDirectParams(params);
        //记录在sub_biz_param表中
        subBizParamsBiz.doRecordDirectSubBizParams(params.getMerchant_sn(), PaywayEnum.ALIPAY.getValue());
        if (!Objects.equals(dto.getSceneTemplateCode(), CommonConstants.BUSINESS_LOG_NO_LOG)) {
            MerchantProviderParamsDto after = new MerchantProviderParamsDto();
            after.setPayway(TradeConstants.PAYWAY_ALIPAY2);
            after.setExtra(JSONObject.parseObject(JSONObject.toJSONString(params), Map.class));
            businessLogBiz.sendMerchantProviderParamsLog(null, after, dto, Lists.newArrayList("extra", "payway"), params.getMerchant_id());
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addWeixinDirectParams(WeixinDirectParams params) {
        return addWeixinDirectParamsWithLog(params, new LogParamsDto().setSceneTemplateCode(CommonConstants.BUSINESS_LOG_NO_LOG));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addWeixinDirectParamsWithLog(WeixinDirectParams params, LogParamsDto dto) {
        if (Objects.isNull(dto) || StringUtils.isEmpty(dto.getSceneTemplateCode())) {
            throw new ContractBizException(CommonConstants.EXCEPTION_ERROR_MSG);
        }
        DirectParamsBizFactory.getDirectParamsBiz(PaywayEnum.WEIXIN.getValue()).addDirectParams(params);
        //记录在sub_biz_param表中
        subBizParamsBiz.doRecordDirectSubBizParams(params.getMerchant_sn(), PaywayEnum.WEIXIN.getValue());
        if (!Objects.equals(dto.getSceneTemplateCode(), CommonConstants.BUSINESS_LOG_NO_LOG)) {
            MerchantProviderParamsDto after = new MerchantProviderParamsDto();
            after.setPayway(TradeConstants.PAYWAY_WEIXIN);
            after.setExtra(JSONObject.parseObject(JSONObject.toJSONString(params), Map.class));
            businessLogBiz.sendMerchantProviderParamsLog(null, after, dto, Lists.newArrayList("extra", "payway"), params.getMerchant_id());
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addAlipayIntlDirectParams(AlipayIntlDirectParams params) {
        return addAlipayIntlDirectParamsWithLog(params, new LogParamsDto().setSceneTemplateCode(CommonConstants.BUSINESS_LOG_NO_LOG));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addAlipayIntlDirectParamsWithLog(AlipayIntlDirectParams params, LogParamsDto dto) {
        if (Objects.isNull(dto) || StringUtils.isEmpty(dto.getSceneTemplateCode())) {
            throw new ContractBizException(CommonConstants.EXCEPTION_ERROR_MSG);
        }
        DirectParamsBizFactory.getDirectParamsBiz(PaywayEnum.ALIPAY_INTL.getValue()).addDirectParams(params);
        if (!Objects.equals(dto.getSceneTemplateCode(), CommonConstants.BUSINESS_LOG_NO_LOG)) {
            MerchantProviderParamsDto after = new MerchantProviderParamsDto();
            after.setPayway(TradeConstants.PAYWAY_ALIPAY_INTL);
            after.setExtra(JSONObject.parseObject(JSONObject.toJSONString(params), Map.class));
            businessLogBiz.sendMerchantProviderParamsLog(null, after, dto, Lists.newArrayList("extra", "payway"), params.getMerchant_id());
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addWeixinHKDirectParams(WeixinHKDirectParams params) {
        return addWeixinHKDirectParamsWithLog(params, new LogParamsDto().setSceneTemplateCode(CommonConstants.BUSINESS_LOG_NO_LOG));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addWeixinHKDirectParamsWithLog(WeixinHKDirectParams params, LogParamsDto dto) {
        if (Objects.isNull(dto) || StringUtils.isEmpty(dto.getSceneTemplateCode())) {
            throw new ContractBizException(CommonConstants.EXCEPTION_ERROR_MSG);
        }
        DirectParamsBizFactory.getDirectParamsBiz(PaywayEnum.WEIXIN_HK.getValue()).addDirectParams(params);
        if (!Objects.equals(dto.getSceneTemplateCode(), CommonConstants.BUSINESS_LOG_NO_LOG)) {
            MerchantProviderParamsDto after = new MerchantProviderParamsDto();
            after.setPayway(TradeConstants.PAYWAY_WEIXIN_HK);
            after.setExtra(JSONObject.parseObject(JSONObject.toJSONString(params), Map.class));
            businessLogBiz.sendMerchantProviderParamsLog(null, after, dto, Lists.newArrayList("extra", "payway"), params.getMerchant_id());
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addBestpayDirectParams(BestpayDirectParams params) {
        DirectParamsBizFactory.getDirectParamsBiz(PaywayEnum.BESTPAY.getValue()).addDirectParams(params);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addBestpayDirectParamsWithLog(BestpayDirectParams params, LogParamsDto dto) {
        if (Objects.isNull(dto) || StringUtils.isEmpty(dto.getSceneTemplateCode())) {
            throw new ContractBizException(CommonConstants.EXCEPTION_ERROR_MSG);
        }
        DirectParamsBizFactory.getDirectParamsBiz(PaywayEnum.BESTPAY.getValue()).addDirectParams(params);
        if (!Objects.equals(dto.getSceneTemplateCode(), CommonConstants.BUSINESS_LOG_NO_LOG)) {
            MerchantProviderParamsDto after = new MerchantProviderParamsDto();
            after.setPayway(TradeConstants.PAYWAY_BESTPAY);
            after.setExtra(JSONObject.parseObject(JSONObject.toJSONString(params), Map.class));
            businessLogBiz.sendMerchantProviderParamsLog(null, after, dto, Lists.newArrayList("extra", "payway"), params.getMerchant_id());
        }
        return true;
    }

    @Override
    public ListResult<MerchantProviderParamsCustomDto> findMerchantProviderParamsList(PageInfo pageInfo,
                                                                                      MerchantProviderParamsCustomDto merchantProviderParamsCustomDto) {
        if (merchantProviderParamsCustomDto == null) {
            merchantProviderParamsCustomDto = new MerchantProviderParamsCustomDto();
        }

        if (WosaiStringUtils.isEmpty(merchantProviderParamsCustomDto.getMerchant_sn())
                && WosaiStringUtils.isEmpty(merchantProviderParamsCustomDto.getPay_merchant_id())
                && WosaiStringUtils.isEmpty(merchantProviderParamsCustomDto.getProvider_merchant_id())) {
            return new ListResult<>(new ArrayList<>(0));
        }

        pageInfo = PageInfoHelper.checkoutPageInfo(pageInfo);
        String orderBy = StringUtil.orderByListToOrderByClause(pageInfo.getOrderBy(), "ctime DESC");

        MerchantProviderParamsCustom queryParam = new MerchantProviderParamsCustom();
        BeanUtils.copyProperties(merchantProviderParamsCustomDto, queryParam);
        PageHelper.startPage(pageInfo.getPage(), pageInfo.getPageSize(), orderBy);
        queryParam.setDeleted(false);
        List<MerchantProviderParamsCustom> merchantProviderParamsCustomList = this.merchantProviderParamsMapper.selectByExampleCustom(queryParam);

        //列表查询没有规则的子商户参数
        merchantProviderParamsCustomList.forEach(custom -> {
            setChannelIfNecessary(custom);
        });

        List<MerchantProviderParamsCustomDto> merchantProviderParamsDtoList = new ArrayList<>(merchantProviderParamsCustomList.size());

        // 商户信息
        List<String> merchantSns = new ArrayList<>(merchantProviderParamsCustomList.size());
        merchantProviderParamsCustomList.forEach(merchantProviderParams -> merchantSns.add(merchantProviderParams.getMerchant_sn()));
        Map<String, Map> merchants = this.getMerchantInfoBySns(pageInfo, merchantSns);

        merchantProviderParamsCustomList.forEach(merchantProviderParams -> {
            MerchantProviderParamsCustomDto entity = new MerchantProviderParamsCustomDto();
            BeanUtils.copyProperties(merchantProviderParams, entity);
            entity.setExtra(CommonUtil.bytes2Map(merchantProviderParams.getExtra()));
//            entity.setMerchant_name(MapUtils.getString(merchants.get(merchantProviderParams.getMerchant_sn()), "name"));
            entity.setMerchant_alias(MapUtils.getString(merchants.get(merchantProviderParams.getMerchant_sn()), Merchant.ALIAS));

            merchantProviderParamsDtoList.add(entity);
        });

        long total = ((Page) merchantProviderParamsCustomList).getTotal();

        ListResult<MerchantProviderParamsCustomDto> listResult = new ListResult<>(paramsBiz.handleDirectParams(merchantProviderParamsDtoList));
        listResult.setTotal(total);
        return listResult;
    }

    Map<String, Map> getMerchantInfoBySns(@NotNull PageInfo pageInfo, @NotNull List<String> merchantSns) {
        Map<String, Map> merchantMap = new HashMap<>();

        Map queryFilter = new HashMap(1);
        queryFilter.put("merchant_sns", merchantSns);
        com.wosai.upay.common.bean.ListResult listResult = this.merchantService.findMerchants(pageInfo, queryFilter);
        if (listResult.getRecords() == null) {
            return new HashMap<>(0);
        }
        listResult.getRecords().forEach(map -> merchantMap.put(MapUtils.getString(map, Merchant.SN), map));

        return merchantMap;
    }

    @Override
    public MerchantProviderParamsCustomDto findMerchantProviderParamsDetailById(String id) {
        // 获取交易参数
        MerchantProviderParamsCustom providerParams = this.merchantProviderParamsMapper.selectByPrimaryKeyCustom(id);

        if (providerParams == null) {
            throw new CommonDataObjectNotExistsException("该交易参数不存在");
        }
        if (providerParams.getDeleted() != null && providerParams.getDeleted()) {
            throw new CommonDataObjectNotExistsException("该交易参数已删除");
        }
        // channel为空，且不是直连
        setChannelIfNecessary(providerParams);
        MerchantProviderParamsCustomDto dto = new MerchantProviderParamsCustomDto();
        BeanUtils.copyProperties(providerParams, dto);
        if (providerParams.getExtra() != null && providerParams.getExtra().length > 0) {
            try {
                Map extraMap = JSON.parseObject(providerParams.getExtra(), Map.class);
                dto.setExtra(extraMap);
            } catch (Exception e) {
                log.warn("获取extra反序列化失败 {} ", providerParams.getId());
            }
        }
        if (dto.getExtra() == null) {
            dto.setExtra(new HashMap<>(2));
        }

        if (dto.getPayway().equals(PaywayEnum.WEIXIN.getValue()) && WosaiStringUtils.isNotEmpty(dto.getParent_merchant_id())) {
            try {
                WxMchInfo wxMchInfo = composeAcquirerBiz.getWxMchInfo(providerParams);
                MchInfo mchInfo = wxMchInfo.getMchInfo();
                SubdevConfigResp subdevConfigResp = wxMchInfo.getSubdevConfig();

                if (mchInfo != null) {
                    Map mchInfoMap = JSON.parseObject(JSON.toJSONString(mchInfo), Map.class);
                    String[] categories = this.businessCategory.getWeixinBusinessCategory().get(mchInfo.getBusiness());
                    if (categories != null && categories.length >= 3) {
                        mchInfoMap.put("business1_name", categories[0]);
                        mchInfoMap.put("business2_name", categories[1]);
                        mchInfoMap.put("business3_name", categories[2]);
                    }
                    // 获取微信商户信息
                    dto.getExtra().put("pay_mch_info", mchInfoMap);
                    dto.getExtra().put("contract_time", dto.getCtime());
                }

                List<WeixinSubAppidDto> appidConfigList = new ArrayList<>();
                if (subdevConfigResp != null && WosaiCollectionUtils.isNotEmpty(subdevConfigResp.getAppid_config_list())) {
                    for (SubdevConfigResp.AppidConfig appidConfig : subdevConfigResp.getAppid_config_list()) {
                        Map appIdInfo = (Map) memoApolloConfig.getAppIdInfos().get(appidConfig.getSub_appid());
                        WeixinSubAppidDto subAppidDto = new WeixinSubAppidDto()
                                .setSub_appid(appidConfig.getSub_appid())
                                .setSubscribe_appid(appidConfig.getSubscribe_appid())
                                .setType(BeanUtil.getPropInt(appIdInfo, "type", 1))
                                .setApp(BeanUtil.getPropString(appIdInfo, "app", "门店码"));
                        appidConfigList.add(subAppidDto);
                    }
                }
                dto.getExtra().put("appid_config_list", appidConfigList);
//                //本地记录未实名
//               if(Objects.equals(dto.getAuth_status(),0)) {
//                   //调用微信查询实名授权
//                   final Boolean authStatus = wxChannelBiz.getAuthStatus(providerParams,providerParams.getProvider());
//                   dto.setAuth_status(authStatus ? 1 : 0);
//               }
            } catch (Exception e) {
                log.error("查询微信信息异常 {} {}", providerParams.getMerchant_sn(), providerParams.getPay_merchant_id(), e);
            }
            dto.setChannel_no(ruleContext.getContractChannel(dto.getPayway(), String.valueOf(dto.getProvider()), dto.getChannel_no()).getPayway_channel_no());
        } else if (PaywayEnum.ALIPAY.getValue().equals(dto.getPayway()) && WosaiStringUtils.isNotEmpty(dto.getParent_merchant_id())) {
            ContractChannel contractChannel = ruleContext.getContractChannel(dto.getChannel());
            if (contractChannel == null) {
                return dto;
            }
            Map providerMap = contractChannel.getChannelParam();
            if (MapUtils.isEmpty(providerMap)) {
                return dto;
            }
            Map subMchMap = composeAcquirerBiz.getAlipayMchInfo(providerParams);
            Map alipaySubMchMap = getWanmaFieldMap(subMchMap);
            if (MapUtils.isEmpty(alipaySubMchMap)) {
                return dto;
            }

            // 行业类目
            alipaySubMchMap.put("business1_name", this.businessCategory.getAlipayBusinessCategory().get(BeanUtil.getPropString(alipaySubMchMap, LakalaWanmaBusinessFileds.BUSINESS)));
            // 区域 name
            String districtCode = MapUtils.getString(alipaySubMchMap, "district_code");
            String names = districtsService.getLocationByCode(districtCode.substring(0, 6)).getNames();
            if (!StringUtils.isEmpty(names)) {
                String[] namesArray = names.split(" ");
                if (namesArray.length == 3) {
                    alipaySubMchMap.put("province", namesArray[0]);
                    alipaySubMchMap.put("city", namesArray[1]);
                    alipaySubMchMap.put("district", namesArray[2]);
                }
            }
            dto.setChannel_no(ruleContext.getContractChannel(dto.getPayway(), String.valueOf(dto.getProvider()), dto.getChannel_no()).getPayway_channel_no());
            dto.getExtra().put("pay_mch_info", alipaySubMchMap);
            dto.getExtra().put("contract_time", dto.getCtime());
        } else if (PaywayEnum.UNIONPAY.getValue().equals(dto.getPayway())) {
            MerchantProviderParamsExt unionOpen = merchantProviderParamsExtMapper.getUnionOpen(providerParams.getId());
            if (!ObjectUtils.isEmpty(unionOpen) && unionOpen.isUnionAudited()) {
                dto.setUnionOpenSystem(unionOpen.getExt_field_2());
            }
        }
        Map payMchInfo = (Map) dto.getExtra().getOrDefault("pay_mch_info", new HashMap<>());
        payMchInfo.put("status", dto.getStatus().equals(UseStatusEnum.NO_USE.getValue()) ? "未使用" : "使用中");
        return dto;
    }

    private Map getWanmaFieldMap(Map subMchMap) {
        if (MapUtils.isEmpty(subMchMap)) {
            return new HashMap();
        }
        Map result = new HashMap(16);
        result.put("mcc", BeanUtil.getPropString(subMchMap, "mcc"));
        result.put("indirect_level", BeanUtil.getPropString(subMchMap, "indirect_level"));
        result.put(LakalaWanmaBusinessFileds.MERCHANT_NAME, BeanUtil.getPropString(subMchMap, "name"));
        result.put(LakalaWanmaBusinessFileds.MERCHANT_SHORTNAME, BeanUtil.getPropString(subMchMap, "alias_name"));
        result.put(LakalaWanmaBusinessFileds.SERVICE_PHONE, BeanUtil.getPropString(subMchMap, "service_phone"));
        result.put(LakalaWanmaBusinessFileds.BUSINESS, BeanUtil.getPropString(subMchMap, "category_id"));
        result.put(LakalaWanmaBusinessFileds.BUSINESS_LICENSE, BeanUtil.getPropString(subMchMap, "business_license"));
        result.put(LakalaWanmaBusinessFileds.BUSINESS_LICENSE_TYPE, BeanUtil.getPropString(subMchMap, "business_license_type"));
        List contactInfos = (List) BeanUtil.getProperty(subMchMap, "contact_info");
        if (!CollectionUtils.isEmpty(contactInfos)) {
            result.put(LakalaWanmaBusinessFileds.CONTACT_NAME, BeanUtil.getPropString(contactInfos.get(0), "name"));
            result.put(LakalaWanmaBusinessFileds.CONTACT_EMAIL, BeanUtil.getPropString(contactInfos.get(0), "email"));
            result.put(LakalaWanmaBusinessFileds.CONTACT_ID_NO, BeanUtil.getPropString(contactInfos.get(0), "id_card_no"));
            result.put(LakalaWanmaBusinessFileds.CONTACT_PHONE, BeanUtil.getPropString(contactInfos.get(0), "phone"));
        }
        List addressInfo = (List) BeanUtil.getProperty(subMchMap, "address_info");
        if (!CollectionUtils.isEmpty(addressInfo)) {
            result.put(LakalaWanmaBusinessFileds.PROVINCE_CODE, BeanUtil.getPropString(addressInfo.get(0), "province_code"));
            result.put(LakalaWanmaBusinessFileds.CITY_CODE, BeanUtil.getPropString(addressInfo.get(0), "city_code"));
            result.put(LakalaWanmaBusinessFileds.DISTRICT_CODE, BeanUtil.getPropString(addressInfo.get(0), "district_code"));
            result.put(LakalaWanmaBusinessFileds.ADDRESS, BeanUtil.getPropString(addressInfo.get(0), "address"));
        }
        List bankCardInfos = (List) BeanUtil.getProperty(subMchMap, "bankcard_info");
        if (!CollectionUtils.isEmpty(bankCardInfos)) {
            result.put(LakalaWanmaBusinessFileds.CARD_NO, BeanUtil.getPropString(bankCardInfos.get(0), "card_no"));
            result.put(LakalaWanmaBusinessFileds.CARD_NAME, BeanUtil.getPropString(bankCardInfos.get(0), "card_name"));
        }
        fillNullValue(result);
        return result;
    }

    private void fillNullValue(Map map) {
        for (Object mapKey : map.keySet().toArray()) {
            if (StringUtils.isEmpty(map.get(mapKey))) {
                map.remove(mapKey);
            }
        }
    }


    /**
     * <AUTHOR>
     * @Description:MerchantProviderParamsCustom 根据规则表join查询。规则不存在的渠道子商户号信息 额外设置渠道信息
     * @time 3:03 下午
     **/
    private void setChannelIfNecessary(MerchantProviderParamsCustom providerParams) {
        if (WosaiStringUtils.isEmpty(providerParams.getChannel()) && !Objects.equals(providerParams.getPayway(), providerParams.getProvider())) {
            try {
                ContractChannel contractChannel = ruleContext.getContractChannel(providerParams.getPayway(), String.valueOf(providerParams.getProvider()), providerParams.getChannel_no());
                providerParams.setChannel(contractChannel.getChannel())
                        .setChannel_name(contractChannel.getName());
                McAcquirerDO mcAcquirer = mcAcquirerDAO.getByAcquirer(contractChannel.getAcquirer());
                if (mcAcquirer != null) {
                    providerParams.setClear_type(mcAcquirer.getClearType())
                            .setAcquirer(mcAcquirer.getAcquirer())
                            .setAcquirer_name(mcAcquirer.getName());
                }
            } catch (com.wosai.upay.common.exception.CommonPubBizException e) {
                log.info("商户 {} 渠道 {} 不存在", providerParams.getMerchant_sn(), providerParams.getChannel_no());
            }
        }
    }


    @Override
    public MerchantProviderParamsDto modifyMerchantProviderParamsById(MerchantProviderParamsDto merchantProviderParamsDto) {

        MerchantProviderParams params = new MerchantProviderParams();
        BeanUtils.copyProperties(merchantProviderParamsDto, params);
        params.setMtime(System.currentTimeMillis());
        if (merchantProviderParamsDto.getExtra() != null) {
            params.setExtra(CommonUtil.map2Bytes(merchantProviderParamsDto.getExtra()));
        }

        int updatedRows = this.merchantProviderParamsMapper.updateByPrimaryKeySelective(params);
        if (updatedRows == 0) {
            throw new CommonDataObjectNotExistsException("不存在该数据");
        }

        return paramsBiz.getParamsById(params.getId());
    }

    @Autowired
    TongLianV2Service tongLianV2Service;

    /**
     * 判断可否切换参数
     * 当前 通联收银宝 只有微信实名认证 可以切参数
     *
     * @param params
     * @param remark
     */
    private void checkChangeTradeParams(MerchantProviderParams params, String remark) {
        if (Objects.isNull(params) || params.getDeleted()) {
            throw new CommonDataObjectNotExistsException("交易参数不存在");
        }
        if (!ProviderEnum.PROVIDER_TONGLIAN_V2.getValue().equals(params.getProvider())) {
            return;
        }
        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(params.getMerchant_sn());
        if (AcquirerTypeEnum.TONG_LIAN_V2.getValue().equals(contractStatus.getAcquirer())) {
            throw new CommonDataObjectNotExistsException("通联收银宝不允许切换交易参数");
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public MerchantProviderParamsDto setDefaultMerchantProviderParams(String id, String feeRate, String remark) {
        return setDefaultMerchantProviderParamsByTradeApp(id, feeRate, remark, subBizParamsBiz.getPayTradeAppId());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public MerchantProviderParamsDto setDefaultMerchantProviderParamsWithLog(String id, String feeRate, String remark, LogParamsDto dto) {
        return setDefaultMerchantProviderParamsByTradeAppWithLog(id, feeRate, remark, subBizParamsBiz.getPayTradeAppId(), dto);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public MerchantProviderParamsDto setDefaultMerchantProviderParamsByTradeApp(String id, String feeRate, String remark, String tradeAppid) {
        return setDefaultMerchantProviderParamsByTradeAppWithLog(id, feeRate, remark, tradeAppid, new LogParamsDto().setSceneTemplateCode(CommonConstants.BUSINESS_LOG_NO_LOG));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public MerchantProviderParamsDto setDefaultMerchantProviderParamsByTradeAppWithLog(String id, String feeRate, String remark, String tradeAppid, LogParamsDto dto) {
        if (Objects.isNull(dto) || StringUtils.isEmpty(dto.getSceneTemplateCode())) {
            throw new ContractBizException(CommonConstants.EXCEPTION_ERROR_MSG);
        }
        try {
            ThreadLocalUtil.setChangeParamsRemark(remark);
            // 判断该 id 是否存在
            MerchantProviderParams providerParams = merchantProviderParamsMapper.selectByPrimaryKey(id);
            if (Objects.isNull(providerParams) || providerParams.getDeleted()) {
                throw new CommonDataObjectNotExistsException("交易参数不存在");
            }
            // 支付业务不允许使用线上收款微信参数
            if (Objects.equals(subBizParamsBiz.getPayTradeAppId(), tradeAppid)
                    && providerParams.isOnlineParams()) {
                throw new CommonPubBizException("支付业务不允许使用线上收款微信参数");
            }
            checkChangeTradeParams(providerParams, remark);
            if (providerParams.getProvider().equals(providerParams.getPayway())) {
                throw new CommonPubBizException("只能设置间连交易参数");
            }
            if (ContractApplicationServiceImpl.ALY_ONLINE__CHANNEL.equals(providerParams.getChannel_no())) {
                throw new CommonPubBizException("线上商户报备渠道不允许切换");
            }
            //保证只有一个间连收单机构
            subBizParamsBiz.checkOnlyOneIndirect(providerParams.getMerchant_sn(), providerParams.getProvider());
            // 切换交易参数
            boolean isChangedTradeParams = tradeParamsBiz.changeTradeParams(providerParams, feeRate, Boolean.FALSE, tradeAppid);
            if (!isChangedTradeParams) {
                throw new CommonPubBizException("切换交易参数失败");
            }
            if (!Objects.equals(dto.getSceneTemplateCode(), CommonConstants.BUSINESS_LOG_NO_LOG)) {
                businessLogBiz.sendMerchantProviderParamsLog(null, genMerchantProviderParamsDto(providerParams), dto, Lists.newArrayList("status", "payway"), null);
            }
            return paramsBiz.getParamsById(id);
        } finally {
            ThreadLocalUtil.clearChangeParamsRemark();
        }
    }

    private MerchantProviderParamsDto genMerchantProviderParamsDto(MerchantProviderParams merchantProviderParams) {
        MerchantProviderParamsDto dto = new MerchantProviderParamsDto();
        dto.setId(merchantProviderParams.getId());
        dto.setMerchant_sn(merchantProviderParams.getMerchant_sn());
        dto.setOut_merchant_sn(merchantProviderParams.getOut_merchant_sn());
        dto.setChannel_no(merchantProviderParams.getChannel_no());
        dto.setParent_merchant_id(merchantProviderParams.getParent_merchant_id());
        dto.setProvider(merchantProviderParams.getProvider());
        dto.setProvider_merchant_id(merchantProviderParams.getProvider_merchant_id());
        dto.setPayway(merchantProviderParams.getPayway());
        dto.setParams_config_status(merchantProviderParams.getParams_config_status());
        dto.setPay_merchant_id(merchantProviderParams.getPay_merchant_id());
        dto.setWeixin_sub_appid(merchantProviderParams.getWeixin_sub_appid());
        dto.setWeixin_subscribe_appid(merchantProviderParams.getWeixin_subscribe_appid());
        dto.setWeixin_sub_mini_appid(merchantProviderParams.getWeixin_sub_mini_appid());
        dto.setWeixin_receipt_appid(merchantProviderParams.getWeixin_receipt_appid());
        dto.setStatus(merchantProviderParams.getStatus());
        dto.setCtime(merchantProviderParams.getCtime());
        dto.setMtime(merchantProviderParams.getMtime());
        dto.setDeleted(merchantProviderParams.getDeleted());
        dto.setVersion(merchantProviderParams.getVersion());
        dto.setContract_rule(merchantProviderParams.getContract_rule());
        dto.setRule_group_id(merchantProviderParams.getRule_group_id());
        dto.setUpdate_status(merchantProviderParams.getUpdate_status());
        dto.setExtra(CommonUtil.bytes2Map(merchantProviderParams.getExtra()));
        dto.setAuth_status(merchantProviderParams.getAuth_status());
        dto.setWx_settlement_id(merchantProviderParams.getWx_settlement_id());
        dto.setWx_use_type(merchantProviderParams.getWx_use_type());
        return dto;
    }

    @Override
    public MerchantProviderParamsDto setDefaultPayMchId(String payMchId, String remark) {
        List<MerchantProviderParamsDto> params = getMerchantProviderParams(new MerchantParamReq().setPay_merchant_id(payMchId));
        if (WosaiCollectionUtils.isEmpty(params)) {
            throw new ContractBizException("子商户号不存在");
        }
        try {
            //如果这个子商户所属收单机构是多业务的,那也要更新一下多业务的子商户号
            subBizParamsBiz.setDefaultForMulti(payMchId);
        } catch (Exception e) {
            log.error("多业务子商户号更新失败,payMchId:{},error:{}", payMchId, e);
        }
        //这是将移动支付业务当前正在用的收单机构的子商户设为默认
        MerchantProviderParamsDto paramsDto = setDefaultMerchantProviderParams(params.get(0).getId(), null, remark);
        return paramsDto;
    }

    @Override
    public String getSetDefaultMerchantProviderParamsMsg(String id) {
        MerchantProviderParams setDefault = merchantProviderParamsMapper.selectByPrimaryKey(id);
        if (setDefault == null) {
            return "交易参数数据不存在";
        }
        if (!PaywayEnum.WEIXIN.getValue().equals(setDefault.getPayway())) {
            return null;
        }
        String merchantSn = setDefault.getMerchant_sn();
        Map merchant = merchantService.getMerchantByMerchantSn(merchantSn);
        MerchantBusinessLicenseInfo license = merchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(BeanUtil.getPropString(merchant, DaoConstants.ID), devCode);
        int type = BeanUtil.getPropInt(license, MerchantBusinessLicence.TYPE, BusinessLicenseTypeEnum.MICRO.getValue());
        MerchantProviderParams use = merchantProviderParamsMapper.getUseWeiXinParam(merchantSn);
        if (use == null) {
            return null;
        }
        if (COLLEGE_CHANNEL.equals(setDefault.getChannel_no())) {
            return "微信高校食堂调整微信间连单日收款额度为无限额";
        }
        if (use.getAuth_status().equals(PayMchAuthStatusEnum.NOT.getValue()) && setDefault.getAuth_status().equals(PayMchAuthStatusEnum.YES.getValue())) {
            if (type == BusinessLicenseTypeEnum.MICRO.getValue()) {
                return "根据子商户号授权的微信商家类型调整微信间连单日收款额度为30w";
            } else {
                return "根据子商户号授权的微信商家类型调整微信间连单日收款额度为无限额";
            }
        }
        if (use.getAuth_status().equals(PayMchAuthStatusEnum.YES.getValue()) && setDefault.getAuth_status().equals(PayMchAuthStatusEnum.NOT.getValue())) {
            return "当前子商户号未授权，设为默认交易参数后微信单日收款额度将调整为5万元";
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public boolean deleteDirectParams(String paramsId, String subPayway, String feeRate) {
        return deleteDirectParamsWithLog(paramsId, subPayway, feeRate, new LogParamsDto().setSceneTemplateCode(CommonConstants.BUSINESS_LOG_NO_LOG));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public boolean deleteDirectParamsWithLog(String paramsId, String subPayway, String feeRate, LogParamsDto dto) {
        if (Objects.isNull(dto) || StringUtils.isEmpty(dto.getSceneTemplateCode())) {
            throw new ContractBizException(CommonConstants.EXCEPTION_ERROR_MSG);
        }
        MerchantProviderParamsDto merchantProviderParams = paramsBiz.getParamsById(paramsId);
        if (merchantProviderParams == null) {
            throw new CommonDataObjectNotExistsException("交易参数不存在");
        }
        if (!merchantProviderParams.getPayway().equals(merchantProviderParams.getProvider())) {
            throw new CommonPubBizException("只能删除直连交易参数");
        }

        // 删除merchant_provider_params对应参数
        DirectParamsBizFactory.getDirectParamsBiz(merchantProviderParams.getPayway()).deleteDirectParams(merchantProviderParams, subPayway, feeRate);

        if (!Objects.equals(dto.getSceneTemplateCode(), CommonConstants.BUSINESS_LOG_NO_LOG)) {
            businessLogBiz.sendMerchantProviderParamsLog(merchantProviderParams, null, dto, null, null);
        }

        return true;
    }

    @Override
    public boolean deleteDirectParamsByMerchantId(String merchantId, int payway, String subPayway, String feeRate) {
        Map merchant = merchantService.getMerchant(merchantId);
        if (WosaiMapUtils.isEmpty(merchant)) {
            throw new CommonPubBizException("商户不存在");
        }

        String merchantSn = WosaiMapUtils.getString(merchant, Merchant.SN);
        MerchantProviderParamsDto merchantProviderParams = paramsBiz.getDirectParams(merchantSn, payway, payway);

        if (merchantProviderParams == null) {
            throw new CommonDataObjectNotExistsException("交易参数不存在");
        }

        // 删除merchant_provider_params对应参数
        DirectParamsBizFactory.getDirectParamsBiz(merchantProviderParams.getPayway())
                .deleteDirectParams(merchantProviderParams, subPayway, feeRate);

        return true;
    }

    /**
     * todo 此处要修改 不能单纯根据取值不同if else
     *
     * @param merchantId 商户id
     * @return
     */
    @Override
    public List<AcquirerMerchantDto> getAcquirerMerchantInfo(String merchantId) {
        Map merchant = merchantService.getMerchantByMerchantId(merchantId);
        if (org.springframework.util.CollectionUtils.isEmpty(merchant)) {
            throw new CommonDataObjectNotExistsException("不存在该商户");
        }

        List<AcquirerMerchantDto> resultList = new ArrayList<>(1);
        String merchantSn = BeanUtil.getPropString(merchant, Merchant.SN);
        //1,当前商户报过的所有收单机构
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or()
                .andMerchant_snEqualTo(merchantSn)
                .andPaywayEqualTo(PaywayEnum.ACQUIRER.getValue())
                .andDeletedEqualTo(false);
        List<MerchantProviderParams> records = merchantProviderParamsMapper.selectByExample(example);
        final List<MerchantProviderParams> acParams = commonEventHandler.distinctByRule(records);
        //当前商户正在使用的收单机构
        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        for (MerchantProviderParams param : acParams) {
            String lakalaTerminalId = null;
            String lakalaMercId = null;
            if (lklChannelNo.equals(param.getChannel_no()) || lklv3ChannelNo.equalsIgnoreCase(param.getChannel_no())) {
                Map merchantConfig = this.tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, null);
                if (merchantConfig == null) {
                    throw new CommonDataObjectNotExistsException("不存在该商户");
                }
                Map params = JSON.parseObject(JSON.toJSONString(merchantConfig.get(MerchantConfig.PARAMS)), Map.class);
                if (params != null) {
                    Map lakalaTradeParams = JSON.parseObject(MapUtils.getString(params, TransactionParam.LAKALA_TRADE_PARAMS));
                    lakalaTerminalId = MapUtils.getString(lakalaTradeParams, TransactionParam.LAKALA_TERM_ID);
                    lakalaMercId = MapUtils.getString(lakalaTradeParams, TransactionParam.LAKALA_MERC_ID);
                }
            }

            //provider
            final String provider = String.valueOf(param.getProvider());

            ContractChannel contractChannel = ruleContext.getContractChannel(param.getPayway(), provider, param.getChannel_no());

            //收单机构
            final String acquirer = contractChannel.getAcquirer();
            //收单机构中文名称
            final String acquireName = mcAcquirerDAO.getAcquirerName(acquirer);

            //银商取parent
            String p = McConstant.RULE_GROUP_UMS.equals(acquirer) ? param.getParent_merchant_id() : param.getProvider_merchant_id();
            //收单机构商户id
            String acquirerMerchantId = StringUtils.isEmpty(lakalaMercId) ? p : lakalaMercId;
            AcquirerMerchantDto acquirerMerchantDto = new AcquirerMerchantDto()
                    .setContractTime(param.getCtime())
                    .setAcquirerTerminalNo(lakalaTerminalId)
                    .setAcquirerMerchantId(acquirerMerchantId)
                    .setAcquirer(acquirer)
                    .setAcquirerName(acquireName)
                    .setProvider(provider)
                    .setStatus(Objects.equals(acquirer, contractStatus.getAcquirer()) ? 1 : 0)
                    .setDisableStatus(param.getDisable_status())
                    .setDisableReasons(merchantTradeParamsBiz.getDisableReasons(param.getExtra()));
            resultList.add(acquirerMerchantDto);
        }
        return resultList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MerchantProviderParamsDto addWeixinSubAppid(String id, WeixinSubAppidDto appidDto) {
        return addWeixinSubAppidWithLog(id, appidDto, new LogParamsDto().setSceneTemplateCode(CommonConstants.BUSINESS_LOG_NO_LOG));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MerchantProviderParamsDto addWeixinSubAppidWithLog(String id, WeixinSubAppidDto appidDto, LogParamsDto dto) {
        try {
            if (Objects.isNull(dto) || StringUtils.isEmpty(dto.getSceneTemplateCode())) {
                throw new ContractBizException(CommonConstants.EXCEPTION_ERROR_MSG);
            }
            //更新MerchantProviderParams表中extra并且将sub_appid配置到微信
            final MerchantProviderParamsDto providerParamsDto = justAddWeixinSubAppid(id, appidDto);
            MerchantProviderParamsDto paramsDto = paramsBiz.getParamsById(id);
            if (!Objects.equals(dto.getSceneTemplateCode(), CommonConstants.BUSINESS_LOG_NO_LOG)) {
                paramsDto.setWeixin_sub_appid(appidDto.getSub_appid());
                businessLogBiz.sendMerchantProviderParamsLog(null, paramsDto, dto, Lists.newArrayList("weixin_sub_appid", "payway"), null);
            }
            return providerParamsDto;
        } catch (Exception e) {
            String originalMsg = ExceptionUtil.getThrowableMsg(e);
            ErrorInfo errorInfo = errorCodeManageBiz.getPromptMessageFromErrorCodeManager("spa", originalMsg, ErrorCodeManageBiz.PLATFORM_WECHAT_SUB_DEV_CONFIG);
            throw new ContractBizException(errorInfo.getMsg(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MerchantProviderParamsDto justAddWeixinSubAppid(String id, WeixinSubAppidDto appidDto) {
        // 设为null是因为这个字段现在都被设置成了小程序appid，会报错，在这个接口中目前没有场景需要配推荐关注公众号
        appidDto.setSubscribe_appid(null);
        MerchantProviderParamsDto paramsDto = paramsBiz.getParamsById(id);

        // 特定的subappid忽略
        if (memoApolloConfig.getSkipAppids().contains(appidDto.getSub_appid())) {
            return paramsDto;
        }

        if (paramsDto == null) {
            throw new CommonDataObjectNotExistsException("交易参数不存在");
        }
        if (!paramsDto.getPayway().equals(PaywayEnum.WEIXIN.getValue())) {
            throw new CommonInvalidParameterException("只能为微信添加 sub_appid");
        }
        if (paramsDto.getPayway().equals(paramsDto.getProvider())) {
            throw new CommonInvalidParameterException("只能为间连微信添加 sub_appid");
        }

        Map extra = paramsDto.getExtra();
        List list = (List) extra.getOrDefault("appid_config_list", new ArrayList<>());
        final List<WeixinSubAppidDto> collect = (List<WeixinSubAppidDto>) list.stream().map(x -> JSONObject.parseObject(JSONArray.toJSONString(x), WeixinSubAppidDto.class)).collect(Collectors.toList());
        //将list转化为key为subAppId,value为WeixinSubAppidDto对象
        final Map<String, WeixinSubAppidDto> appidDtoMap = collect.stream().collect(Collectors.toMap(item -> item.getSub_appid(), item -> item, (val1, val2) -> val1));
        //如果有则更新,没有则新增
        appidDtoMap.put(appidDto.getSub_appid(), appidDto);
        final Collection<WeixinSubAppidDto> weixinSubAppidDtos = appidDtoMap.values();
        extra.put("appid_config_list", weixinSubAppidDtos);
        paramsBiz.updateByPrimaryKeySelective(
                new MerchantProviderParamsDto().setId(paramsDto.getId())
                        .setExtra(extra)
        );
        log.info("justAddWeixinSubAppid id:{}; merchant_sn:{}; extra:{}", paramsDto.getId(), paramsDto.getMerchant_sn(), extra);
        BasicProvider provider = providerFactory.getProvider(String.valueOf(paramsDto.getProvider()));
        if (Objects.isNull(provider)) {
            throw new ContractBizException("暂不支持配置微信appid");
        }

        WeixinSubDevResp resp = provider.weixinSubDevConfig(appidDto.getSub_appid(), paramsBiz.fromDO(paramsDto));
        if (Objects.equals(0, resp.getCode())) {
            throw new CommonPubBizException("微信添加appid失败：" + resp.getMessage());
        }

        return paramsBiz.getParamsById(id);
    }

    @Override
    public MerchantProviderParamsDto addWeixinSubAppIdBySubMchId(String wxSubMchId, String subAppId) {
        MerchantParamReq req = new MerchantParamReq()
                .setPay_merchant_id(wxSubMchId);
        List<MerchantProviderParamsDto> params = getMerchantProviderParams(req);
        if (WosaiCollectionUtils.isNotEmpty(params)) {
            WeixinSubAppidDto dto = new WeixinSubAppidDto()
                    .setSub_appid(subAppId)
                    .setType(1);
            return justAddWeixinSubAppid(params.get(0).getId(), dto);
        }
        throw new CommonPubBizException(wxSubMchId + " 子账户号不存在");
    }

    @Override
    public String getSchoolCanteenMchId(String merchantSn) {
        String channelNo = composeAcquirerBiz.getSchoolCanteenChannelNo(merchantSn);
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(merchantSn)
                .andChannel_noEqualTo(channelNo)
                .andDeletedEqualTo(false);
        example.setOrderByClause("ctime desc");
        List<MerchantProviderParams> params = merchantProviderParamsMapper.selectByExample(example);
        return WosaiCollectionUtils.isEmpty(params) ? null : params.get(0).getPay_merchant_id();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setDefaultSubAppid(String id, WeixinSubAppidDto appidDto) {
        MerchantProviderParamsDto paramsDto = paramsBiz.getParamsById(id);
        if (paramsDto == null) {
            throw new CommonDataObjectNotExistsException("交易参数不存在");
        }
        if (!paramsDto.getPayway().equals(PaywayEnum.WEIXIN.getValue())) {
            throw new CommonInvalidParameterException("只能为微信添加小程序appid");
        }
        if (appidDto.getType() != 2) {
            throw new CommonInvalidParameterException("只能选择小程序appid");
        }
        Map<String, Object> extra = paramsDto.getExtra();
        Object obj = MapUtils.getObject(extra, "appid_config_list");
        List<WeixinSubAppidDto> weixinSubAppidDtos = JSON.parseArray(JSON.toJSONString(obj), WeixinSubAppidDto.class);
        if (!ObjectUtils.isEmpty(paramsDto.getWeixin_sub_appid())) {
            weixinSubAppidDtos.add(new WeixinSubAppidDto().setType(1).setSub_appid(paramsDto.getWeixin_sub_appid()).setSub_appid(paramsDto.getWeixin_subscribe_appid()));
        }
        if (!ObjectUtils.isEmpty(paramsDto.getWeixin_sub_mini_appid())) {
            weixinSubAppidDtos.add(new WeixinSubAppidDto().setType(2).setSub_appid(paramsDto.getWeixin_sub_mini_appid()));
        }
        String targetAppid = appidDto.getSub_appid();
        Boolean flag = true;
        for (WeixinSubAppidDto weixinSubAppidDto : weixinSubAppidDtos) {
            if (targetAppid.equalsIgnoreCase(weixinSubAppidDto.getSub_appid()) && weixinSubAppidDto.getType() == 2) {
                String nestProperty = CommonModel.PROVIDER_KEY.get(String.valueOf(paramsDto.getProvider())) + ".weixin_mini_sub_appid";
                merchantConfigParamsBiz.updateMerchantConfigParamsV2(null, paramsDto.getMerchant_sn(), PaywayEnum.WEIXIN.getValue(), nestProperty, targetAppid);
                flag = false;
                break;
            }
        }
        if (flag) {
            throw new CommonInvalidParameterException("找不到对应的微信小程序appid");
        }
    }


    @Override
    public String getInUsePayMchId(String merchantSn, int payway) {
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(merchantSn)
                .andPaywayEqualTo(payway)
                .andProviderNotEqualTo(payway)
                .andStatusEqualTo(UseStatusEnum.IN_USE.getValue())
                .andDeletedEqualTo(false);
        List<MerchantProviderParams> params = merchantProviderParamsMapper.selectByExample(example);
        return WosaiCollectionUtils.isEmpty(params) ? null : params.get(0).getPay_merchant_id();
    }

    @Override
    public MerchantProviderParamsDto getInUseProviderParams(String merchantSn, int payway) {
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(merchantSn)
                .andPaywayEqualTo(payway)
                .andProviderNotEqualTo(payway)
                .andStatusEqualTo(UseStatusEnum.IN_USE.getValue())
                .andDeletedEqualTo(false);
        List<MerchantProviderParams> params = merchantProviderParamsMapper.selectByExample(example);
        return WosaiCollectionUtils.isEmpty(params) ? null : paramsBiz.toDO(params.get(0));
    }

    @Override
    public boolean syncMerchantConfigToParams(String merchantSn) {
        syncBiz.syncMerchantConfigToParams(merchantSn);
        return true;
    }

    @Override
    public List<MerchantProviderParamsDto> getMerchantProviderParams(MerchantParamReq req) {
        if (WosaiStringUtils.isEmpty(req.getMerchant_sn()) && WosaiStringUtils.isEmpty(req.getPay_merchant_id())
                && WosaiStringUtils.isEmpty(req.getProvider_merchant_id())) {
            throw new CommonInvalidParameterException("merchant_sn 和 pay_merchant_id 不能同时为空");
        }
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        MerchantProviderParamsExample.Criteria criteria = example.or();

        if (WosaiStringUtils.isNotEmpty(req.getMerchant_sn())) {
            criteria.andMerchant_snEqualTo(req.getMerchant_sn());
        }
        if (WosaiStringUtils.isNotEmpty(req.getProvider_merchant_id())) {
            criteria.andProvider_merchant_idEqualTo(req.getProvider_merchant_id());
        }
        if (WosaiStringUtils.isNotEmpty(req.getChannel_no())) {
            criteria.andChannel_noEqualTo(req.getChannel_no());
        }
        if (req.getProvider() != null) {
            criteria.andProviderEqualTo(req.getProvider());
        }
        if (req.getPayway() != null) {
            criteria.andPaywayEqualTo(req.getPayway());
        }
        if (req.getStatus() != null) {
            criteria.andStatusEqualTo(req.getStatus());
        }
        if (WosaiStringUtils.isNotEmpty(req.getPay_merchant_id())) {
            criteria.andPay_merchant_idEqualTo(req.getPay_merchant_id());
        }
        if (WosaiStringUtils.isNotEmpty(req.getProvider_merchant_id())) {
            criteria.andProvider_merchant_idEqualTo(req.getProvider_merchant_id());
        }

        criteria.andDeletedEqualTo(false);
        List<MerchantProviderParams> params = merchantProviderParamsMapper.selectByExampleWithBLOBs(example);
        return params.stream().map(param -> paramsBiz.toDO(param)).collect(Collectors.toList());
    }

    @Override
    public List<WeixinSubAppidAddResp> addWeixinSubAppidForUseAndOnline(WeixinSubAppidAddReq req) {
        if (Objects.isNull(req.getAppidDto()) && WosaiCollectionUtils.isEmpty(req.getPayAuthPath())) {
            throw new CommonInvalidParameterException("appid和支付目录不能同时为空");
        }
        WeixinConfig weixinConfig = new WeixinConfig();
        if (!Objects.isNull(req.getAppidDto())) {
            WeixinAppidConfig weixinAppidConfig = new WeixinAppidConfig();
            weixinAppidConfig.setSub_appid(req.getAppidDto().getSub_appid());
            weixinAppidConfig.setMini(true);
            weixinConfig.setAppidConfigs(Arrays.asList(weixinAppidConfig));
        }
        if (WosaiCollectionUtils.isNotEmpty(req.getPayAuthPath())) {
            weixinConfig.setPayAuthPath(req.getPayAuthPath());
        }


        Optional<MerchantProviderParamsDO> wxUseParams = merchantProviderParamsDAO.getInUseProviderParams(req.getMerchantSn(), PaywayEnum.WEIXIN.getValue());
        Optional<MerchantProviderParamsDO> wxOnlineParams = merchantProviderParamsDAO.getWeixinOnlineMerchantProviderParamsByMerchantSn(req.getMerchantSn());
        List<MerchantProviderParamsDO> params = new ArrayList<>();
        wxUseParams.ifPresent(params::add);
        wxOnlineParams.ifPresent(params::add);
        List<WeixinSubAppidAddResp> result = new ArrayList<>();

        for (MerchantProviderParamsDO param : params) {
            try {
                BasicProvider provider = providerFactory.getProvider(String.valueOf(param.getProvider()));
                if (Objects.isNull(provider)) {
                    throw new ContractBizException("暂不支持微信开发配置");
                }
                weixinConfig.setWeixinMchId(param.getPayMerchantId());
                MerchantProviderParams merchantProviderParams = MerchantProviderParamsConverter.merchantProviderParamsDOToMerchantProviderParams(param);
                WeixinSubDevResp resp = provider.weixinSubDevConfig(weixinConfig, merchantProviderParams);
                if (Objects.equals(0, resp.getCode())) {
                    throw new CommonPubBizException("微信开发配置失败：" + resp.getMessage());
                }

                result.add(new WeixinSubAppidAddResp()
                        .setProviderParamsId(param.getId())
                        .setSuccess(true));
            } catch (Exception e) {
                log.error("微信开发配置失败 providerParamsId: {}", param.getId(), e);
                result.add(new WeixinSubAppidAddResp()
                        .setProviderParamsId(param.getId())
                        .setSuccess(false)
                        .setMessage(e.getMessage()));
            }
        }
        return result;
    }

    @Override
    public boolean updateHxbWxSubMchId(String oriSubId, String tarSubId) {
        final MerchantProviderParams providerParams = merchantProviderParamsMapper.getByPayMerchantId(oriSubId);
        Optional.ofNullable(providerParams)
                .filter(param -> Objects.equals(param.getProvider(), ProviderEnum.PROVIDER_HXB.getValue()) && Objects.equals(param.getPayway(), PaywayEnum.WEIXIN.getValue()))
                .orElseThrow(() -> new CommonPubBizException(String.format("微信子商户号%s未匹配到信息", oriSubId)));
        //payway=3的
        Map map = CommonUtil.bytes2Map(providerParams.getExtra());
        //线下进件
        final Object tradeParams = map.get("tradeParams");
        //api进件
        final Object hxbankTradeParams = map.get("hxbank_trade_params");
        final Map extraMap = (Map) (map.containsKey("hxbank_trade_params") ? map.get("hxbank_trade_params") : map.get("tradeParams"));
        extraMap.put("weixin_sub_mch_id", tarSubId);
        //更新
        MerchantProviderParams newParams = new MerchantProviderParams()
                .setId(providerParams.getId())
                .setParams_config_status(Objects.nonNull(tradeParams) ? providerParams.getParams_config_status() : MerchantProviderParams.PARAMS_CONFIG_STATUS_PRE)
                .setPay_merchant_id(tarSubId)
                .setMtime(System.currentTimeMillis())
                .setExtra(CommonUtil.map2Bytes(map));
        merchantProviderParamsMapper.updateByPrimaryKeySelective(newParams);
        //payway=0的线上进件的也包含相关微信息,但是线下进件的没有,所以也要做逻辑处理
        //线上进件的华夏商户号
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        MerchantProviderParamsExample.Criteria criteria = example.or();
        final String merchantSn = providerParams.getMerchant_sn();
        criteria.andMerchant_snEqualTo(merchantSn).andProviderEqualTo(providerParams.getProvider()).andPaywayEqualTo(0);
        final List<MerchantProviderParams> params = merchantProviderParamsMapper.selectByExampleWithBLOBs(example);
        //华夏进件信息
        final MerchantProviderParams apiParams = params.get(0);
        //线上进件
        if (Objects.isNull(tradeParams) && Objects.nonNull(hxbankTradeParams) && Objects.nonNull(apiParams)) {
            final Map apiMap = CommonUtil.bytes2Map(apiParams.getExtra());
            final Map wxMap = MapUtils.getMap(apiMap, "3");
            wxMap.computeIfPresent("pay_merchant_id", (t, u) -> tarSubId);
            final Map hxbankTradeParamsMap = (Map) BeanUtil.getNestedProperty(wxMap, "trade.hxbank_trade_params");
            hxbankTradeParamsMap.computeIfPresent("weixin_sub_mch_id", (t, u) -> tarSubId);
            //更新
            MerchantProviderParams mainParams = new MerchantProviderParams()
                    .setId(apiParams.getId())
                    .setExtra(CommonUtil.map2Bytes(apiMap));
            merchantProviderParamsMapper.updateByPrimaryKeySelective(mainParams);
        }
        //当前再用收单机构是不是华夏,如果是华夏就要把信息更新到交易
        final ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        if (!Objects.equals(contractStatus.getAcquirer(), AcquirerTypeEnum.HXB.getValue())) {
            return Boolean.TRUE;
        }
        final Map merchant = merchantService.getMerchantByMerchantSn(merchantSn);
        Map weixinConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(BeanUtil.getPropString(merchant, DaoConstants.ID), PaywayEnum.WEIXIN.getValue());
        Map configParams = MapUtils.getMap(weixinConfig, MerchantConfig.PARAMS);
        Map hxConfigParams = MapUtil.getMap(configParams, TransactionParam.HXBANK_TRADE_PARAMS);
        if (MapUtils.isEmpty(hxConfigParams)) {
            return Boolean.TRUE;
        }
        hxConfigParams.computeIfPresent("weixin_sub_mch_id", (t, u) -> tarSubId);
        Map updateConfig = CollectionUtil.hashMap(CommonModel.ID, MapUtils.getString(weixinConfig, CommonModel.ID));
        updateConfig.put(MerchantConfig.PARAMS, configParams);
        tradeConfigService.updateMerchantConfig(updateConfig);
        supportService.removeCachedParams(merchantSn);
        return Boolean.TRUE;
    }

    @Override
    public int deleteInvalidSubMchId(String subMchId) {
        return merchantProviderParamsDAO.logicDeleteByPayMchId(subMchId);
    }

    @Override
    public Map<String, List<MerchantProviderParamsCustomDto>> findMerchantSubBizParams(String merchantSn) {
        SubBizParamsExample example = new SubBizParamsExample();
        example.or().andMerchant_snEqualTo(merchantSn).andDeletedEqualTo(Boolean.FALSE);
        List<SubBizParams> subBizParamsList = subBizParamsMapper.selectByExampleWithBLOBs(example);
        if (CollectionUtils.isEmpty(subBizParamsList)) {
            return Maps.newHashMap();
        }
        //mc_rule_group表group_id与acquire对应关系
        final List<McContractRuleDO> ruleList = mcContractRuleDAO.listAllRule();
        //规则和acquire对应关系
        final Map<String, String> ruleAcquireMap = ruleList.parallelStream().collect(Collectors.toMap(x -> x.getRule(), x -> x.getAcquirer(), (val1, val2) -> val1));
        //排序,以业务方id升序
        subBizParamsList.sort(Comparator.comparing(SubBizParams::getTrade_app_id));
        Map<String, List<MerchantProviderParamsCustomDto>> result = Maps.newHashMap();
        subBizParamsList.stream().forEach(subBizParam -> {
            //参数组装
            String tradeAppId = subBizParam.getTrade_app_id();
            Integer provider = subBizParam.getProvider();
            //业务参数对应的交易参数
            List<String> useParamIds = Optional.ofNullable((List<String>) subBizParam.getExtraMap().get(String.valueOf(provider))).orElseGet(ArrayList::new);
            List<MerchantProviderParamsCustomDto> detailList = Lists.newArrayList();
            if (CollectionUtils.isEmpty(useParamIds) || useParamIds.stream().allMatch(Objects::isNull)) {
                //T9 pos
                final McAcquirerDO mcAcquirerDO = mcAcquirerDAO.getByProvider(String.valueOf(provider));
                MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, Integer.valueOf(mcAcquirerDO.getProvider()));
                MerchantProviderParamsCustomDto customDto = new MerchantProviderParamsCustomDto();
                customDto.setAcquirer_name(mcAcquirerDO.getName())
                        .setPayway(PaywayEnum.BANK_CARD.getValue())
                        .setPay_merchant_id(acquirerParams.getPay_merchant_id())
                        .setProvider_merchant_id(acquirerParams.getProvider_merchant_id());
                detailList.add(customDto);
            } else {
                //其他子业务
                List<MerchantProviderParamsCustomDto> customDtos = useParamIds.stream().map(id -> {
                            MerchantProviderParams param = merchantProviderParamsMapper.selectByPrimaryKey(id);
                            //银行线下导入多业务
                            if (Objects.isNull(param)) {
                                return getMerchantProviderParamsCustomDtos(merchantSn, tradeAppId, provider, id);
                            }
                            if (!Objects.equals(param.getMerchant_sn(), merchantSn) || param.getDeleted()) {
                                return null;
                            }
                            //直连
                            if (Lists.newArrayList(ProviderEnum.ALI_PAY.getValue(), ProviderEnum.WEI_XIN.getValue()).contains(param.getProvider())) {
                                List<MerchantProviderParamsCustomDto> resultList = getDirectMerchantProviderParamsCustomDtos(param);
                                if (b2bAppId.equals(tradeAppId)){
                                    resultList = resultList.stream().filter(params -> Objects.equals("小程序支付", params.getSub_payway())).collect(Collectors.toList());
                                }
                                return resultList;
                            } else {
                                MerchantProviderParamsCustomDto customDto = new MerchantProviderParamsCustomDto();
                                //收单机构
                                final String acquirer = BeanUtil.getPropString(ruleAcquireMap, param.getContract_rule());
                                //收单机构中文名称
                                final String acquireName = mcAcquirerDAO.getAcquirerName(acquirer);
                                customDto.setAcquirer_name(acquireName);
                                customDto.setPayway(param.getPayway())
                                        .setPay_merchant_id(param.getPay_merchant_id())
                                        .setProvider_merchant_id(param.getProvider_merchant_id())
                                        .setWx_use_type(param.getWx_use_type());
                                List<MerchantProviderParamsCustomDto> resultList = Lists.newArrayList();
                                resultList.add(customDto);
                                return resultList;
                            }
                        })
                        .filter(Objects::nonNull)
                        .flatMap(x -> x.stream())
                        .sorted(Comparator.comparingInt(MerchantProviderParamsCustomDto::getPayway))
                        .collect(Collectors.toList());
                detailList.addAll(customDtos);
            }
            Optional.ofNullable(detailList).orElseGet(ArrayList::new)
                    .stream()
                    .filter(Objects::nonNull)
                    .forEach(dto -> dto.setSubEffectTime(subBizParam.getCreate_at()));
            result.merge(subBizParamsBiz.getTradeAppNameById(tradeAppId), detailList, (pre, cur) -> ListUtils.sum(pre, cur));
        });
        BrandMerchantInfoQueryResp brandMerchantInfo = brandBusinessClient.getBrandMerchantInfoByMerchantId(BeanUtil.getPropString(merchantService.getMerchantByMerchantSn(merchantSn), DaoConstants.ID));
        if (brandMerchantInfo.isSubBrandMerchant() && brandMerchantInfo.getPaymentMode().isBrandMode()) {
            BrandDetailInfoQueryResp brandDetailInfo = brandBusinessClient.getBrandDetailInfoByBrandId(brandMerchantInfo.getBrandId());
            if (brandMerchantInfo.getPaymentMode().equals(PaymentModeEnum.WX_BRAND_MODE)) {
                for (Map.Entry<String, List<MerchantProviderParamsCustomDto>> entry : result.entrySet()) {
                    List<MerchantProviderParamsCustomDto> customDtos = entry.getValue().stream().filter(r -> !r.getPayway().equals(PaywayEnum.WEIXIN.getValue())).collect(Collectors.toList());
                    MerchantProviderParamsCustomDto wxBrandParams = getBrandProviderParamsCustomDto(brandDetailInfo.getMainMerchantSn(), PaywayEnum.WEIXIN, ruleAcquireMap);
                    wxBrandParams.setProvider_merchant_id(customDtos.get(0).getProvider_merchant_id());
                    customDtos.add(wxBrandParams);
                    entry.setValue(customDtos);
                }
            } else if (brandMerchantInfo.getPaymentMode().equals(PaymentModeEnum.ALI_BRAND_MODE)) {
                for (Map.Entry<String, List<MerchantProviderParamsCustomDto>> entry : result.entrySet()) {
                    List<MerchantProviderParamsCustomDto> customDtos = entry.getValue().stream().filter(r -> !r.getPayway().equals(PaywayEnum.ALIPAY.getValue())).collect(Collectors.toList());
                    MerchantProviderParamsCustomDto aliBrandParams = getBrandProviderParamsCustomDto(brandDetailInfo.getMainMerchantSn(), PaywayEnum.ALIPAY, ruleAcquireMap);
                    aliBrandParams.setProvider_merchant_id(customDtos.get(0).getProvider_merchant_id());
                    customDtos.add(aliBrandParams);
                    entry.setValue(customDtos);
                }
            } else {
                for (Map.Entry<String, List<MerchantProviderParamsCustomDto>> entry : result.entrySet()) {
                    List<MerchantProviderParamsCustomDto> customDtos = entry.getValue().stream().filter(r -> !r.getPayway().equals(PaywayEnum.ALIPAY.getValue()) && !r.getPayway().equals(PaywayEnum.WEIXIN.getValue())).collect(Collectors.toList());
                    MerchantProviderParamsCustomDto wxBrandParams = getBrandProviderParamsCustomDto(brandDetailInfo.getMainMerchantSn(), PaywayEnum.WEIXIN, ruleAcquireMap);
                    MerchantProviderParamsCustomDto aliBrandParams = getBrandProviderParamsCustomDto(brandDetailInfo.getMainMerchantSn(), PaywayEnum.ALIPAY, ruleAcquireMap);
                    aliBrandParams.setProvider_merchant_id(customDtos.get(0).getProvider_merchant_id());
                    wxBrandParams.setProvider_merchant_id(customDtos.get(0).getProvider_merchant_id());
                    customDtos.add(aliBrandParams);
                    customDtos.add(wxBrandParams);
                    entry.setValue(customDtos);
                }
            }
        }
        return result;
    }

    private MerchantProviderParamsCustomDto getBrandProviderParamsCustomDto(String merchantSn, PaywayEnum paywayEnum, Map<String, String> ruleAcquirerMap) {
        MerchantProviderParams params;
        if (PaywayEnum.WEIXIN.equals(paywayEnum)) {
            params = merchantProviderParamsMapper.getUseWeiXinParam(merchantSn);
        } else {
            params = merchantProviderParamsMapper.getUseAlipayParam(merchantSn);
        }
        final String acquirer = BeanUtil.getPropString(ruleAcquirerMap, params.getContract_rule());
        //收单机构中文名称
        final String acquireName = mcAcquirerDAO.getAcquirerName(acquirer);
        MerchantProviderParamsCustomDto customDto = new MerchantProviderParamsCustomDto();
        customDto.setAcquirer_name(acquireName);
        customDto.setPayway(paywayEnum.getValue())
                .setPay_merchant_id(params.getPay_merchant_id())
                .setProvider_merchant_id(params.getProvider_merchant_id())
                .setWx_use_type(params.getWx_use_type());
        return customDto;
    }

    /**
     * 银行线下导入多业务
     *
     * @param merchantSn
     * @param tradeAppId
     * @param provider
     * @param id
     * @return
     */
    public List<MerchantProviderParamsCustomDto> getMerchantProviderParamsCustomDtos(String merchantSn,
                                                                                     String tradeAppId,
                                                                                     Integer provider,
                                                                                     String id) {
        /**
         * @see SubBizParamsBiz#updateSubBizParamsForBankOffline(String, String, Integer, String, Integer)
         */
        OfflineMultiTrade offlineMultiTrade = offlineMultiTradeMapper.selectByTradeAppId(merchantSn, tradeAppId, String.valueOf(provider));
        if (Objects.isNull(offlineMultiTrade)) {
            return null;
        }

        Optional<McProviderDO> mcProviderDO = mcProviderDAO.getByProvider(String.valueOf(provider));
        if (!mcProviderDO.isPresent()) {
            return null;
        }
        Map<String, Object> extraMap = offlineMultiTrade.getExtraMap();
        //支付方式和基础交易参数的Map
        Map<String, Map<String, String>> payWayParamMap = (Map<String, Map<String, String>>) org.apache.commons.collections.MapUtils.getMap(extraMap, TRADEPARAMS);
        Map<String, String> payMerchantIdPayWayMap = new HashMap<>(3);
        payWayParamMap.forEach((k, v) -> {
            if (v.containsValue(id)) {
                payMerchantIdPayWayMap.put(id, k);
            }
        });
        McProviderDO mcProvider = mcProviderDO.get();
        MerchantProviderParamsCustomDto customDto = new MerchantProviderParamsCustomDto();
        customDto.setAcquirer_name(mcProvider.getName());
        customDto.setPayway(MapUtils.getInteger(payMerchantIdPayWayMap, id))
                .setPay_merchant_id(id)
                .setProvider_merchant_id(offlineMultiTrade.getBank_merchant_sn());
        List<MerchantProviderParamsCustomDto> resultList = Lists.newArrayList();
        resultList.add(customDto);
        return resultList;
    }

    @Override
    public boolean checkSettleId(String id) {
        MerchantProviderParams params = merchantProviderParamsMapper.selectByPrimaryKey(id);
        return wxSettlementIdChangeBiz.checkSettlementId(params);
    }

    @Override
    public List<MerchantProviderParamsDto> queryCurrentAcquirerWeixinParams(String merchantSn) {
        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        if (Objects.isNull(contractStatus)) {
            return new ArrayList<>();
        }
        List<Integer> providers = mcProviderDAO.listByAcquirer(contractStatus.getAcquirer()).stream()
                .map(r -> Integer.valueOf(r.getProvider())).collect(Collectors.toList());
        if (WosaiCollectionUtils.isEmpty(providers)) {
            return new ArrayList<>();
        }
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        MerchantProviderParamsExample.Criteria criteria = example.or();
        criteria.andMerchant_snEqualTo(merchantSn)
                .andProviderIn(providers)
                .andPaywayEqualTo(PaywayEnum.WEIXIN.getValue())
                .andDeletedEqualTo(false);
        List<MerchantProviderParams> merchantProviderParams = merchantProviderParamsMapper.selectByExampleWithBLOBs(example);
        return merchantProviderParams.stream().map(param -> paramsBiz.toDO(param)).collect(Collectors.toList());
    }

    /**
     * 直连交易参数处理
     *
     * @param param
     * @return
     */
    public List<MerchantProviderParamsCustomDto> getDirectMerchantProviderParamsCustomDtos(MerchantProviderParams param) {
        MerchantProviderParamsCustomDto directDTO = new MerchantProviderParamsCustomDto();
        BeanUtils.copyProperties(param, directDTO);
        directDTO.setExtra(CommonUtil.bytes2Map(param.getExtra()));
        //直连商户数据可能有多个getPay_merchant_id,但是如果getPay_merchant_id相同就变成一条
        List<MerchantProviderParamsCustomDto> originDirectList = DirectParamsBizFactory.getDirectParamsBiz(param.getProvider()).handleDirectParams(directDTO);
        List<MerchantProviderParamsCustomDto> resultList = originDirectList.stream().map(directParam -> {
            directParam.setPayway(directParam.getPayway())
                    .setPay_merchant_id(directParam.getPay_merchant_id())
                    .setProvider_merchant_id(directParam.getProvider_merchant_id())
                    .setWx_use_type(directParam.getWx_use_type());
            directParam.setAcquirer_name(Objects.equals(directParam.getProvider(), ProviderEnum.ALI_PAY.getValue()) ? "支付宝直连" : "微信直连");
            return directParam;
        }).collect(Collectors.toMap(dto -> dto.getPay_merchant_id(), dto -> dto, (v1, v2) -> {
            String join = Joiner.on(",").skipNulls().join(v1.getSub_payway(), v2.getSub_payway());
            v1.setSub_payway(join);
            return v1;
        })).entrySet().stream().map(Map.Entry::getValue).collect(Collectors.toList());
        return resultList;
    }


    @Override
    public void openHuaBei(String merchantSn) {
        subBizParamsBiz.openHuaBei(merchantSn);
    }

    @Override
    public void closeHuaBei(String merchantSn) {
        subBizParamsBiz.closeHuaBei(merchantSn);
    }

    @Override
    public void openCrossCityPayment(String merchantSn) {
        subBizParamsBiz.openCrossCityPayment(merchantSn, PaywayEnum.WEIXIN.getValue());
    }

    @Override
    public void openCrossCityPayment(CrossCityPaymentOpenReq crossCityPaymentOpenReq) {
        subBizParamsBiz.openCrossCityPayment(crossCityPaymentOpenReq.getMerchantSn(), crossCityPaymentOpenReq.getPayway());
    }

    @Override
    public ContractResponse closeCrossCityPayment(CrossCityPaymentCloseReq crossCityPaymentCloseReq) {
        try {
            subBizParamsBiz.closeCrossCityPayment(crossCityPaymentCloseReq.getMerchantSn(), crossCityPaymentCloseReq.getPayway());
            return new ContractResponse().setSuccess(true);
        } catch (Exception e) {
            log.error("关闭跨城收款异常 {}", JSON.toJSONString(crossCityPaymentCloseReq), e);
            return new ContractResponse().setSuccess(false).setMsg(e.getMessage());
        }
    }

    /**
     * 查询子商户号授权状态
     *
     * @param subMchId 子商户号
     * @return 查询子商户号授权状态
     */
    @Override
    public boolean queryMchIdAuthStatus(@NotBlank(message = "子商户号不能为空") String subMchId) {
        MerchantProviderParams merchantProviderParamsDto = merchantProviderParamsMapper.selectByPayMerchantId(subMchId);
        if (merchantProviderParamsDto == null) {
            throw new ContractBizException("未查到目标子商户号");
        }
        if (merchantProviderParamsDto.getPayway().equals(PaywayEnum.WEIXIN.getValue())) {
            return wechatAuthBiz.getAuthStatusImmediately(merchantProviderParamsDto.getPay_merchant_id(), merchantProviderParamsDto.getChannel_no(), merchantProviderParamsDto.getId(), merchantProviderParamsDto.getProvider());
        } else if (merchantProviderParamsDto.getPayway().equals(PaywayEnum.ALIPAY.getValue())) {
            return alipayAuthBiz.queryAuthByMchId(merchantProviderParamsDto);
        }
        throw new ContractSysException("非支付宝微信子商户号不支持查询");
    }


    /**
     * 通道开通/切换限制商户同1时间只能生效1个第三方收单机构通道
     *
     * @param merchantSn 商户SN
     * @param provider   提供者
     * @return true-允许,false_不允许
     */
    @Override
    public void allowOpenBusiness(String merchantSn, Integer provider) {
        subBizParamsBiz.checkOnlyOneIndirect(merchantSn, provider);
    }


    @Override
    public void openMultiTrade(String merchantSn, String tradeAppId) {
        DataBusAppInfo appInfo = buildDataBusAppInfo(merchantSn, tradeAppId);
        subBizParamsBiz.openSmart(appInfo);
    }


    @Override
    public MultiOpenCheckResp checkOpenMultiTrade(String merchantSn, String tradeAppId) {
        DataBusAppInfo appInfo = buildDataBusAppInfo(merchantSn, tradeAppId);
        return subBizParamsBiz.checkOpenSmart(appInfo);
    }


    private DataBusAppInfo buildDataBusAppInfo(String merchantSn, String tradeAppId) {
        //tradeAppId和appId关系
        Map appIdSubBizMap = applicationApolloConfig.getAppIdSubBiz();
        Map<String, String> tradeAppIdMap = Maps.newHashMap();
        appIdSubBizMap.forEach((k, v) -> {
            SubBizConfig subBizConfig = JSON.parseObject(JSON.toJSONString(v), SubBizConfig.class);
            tradeAppIdMap.put(subBizConfig.getMappingTradeAppId(), String.valueOf(k));
        });
        //crm定义的每一个应用对应的唯一标识
        final String appId = tradeAppIdMap.get(tradeAppId);
        if (StringUtils.isEmpty(appId)) {
            throw new CommonPubBizException(String.format("tradeAppId 未找到"));
        }
        final Map merchant = merchantService.getMerchantBySn(merchantSn);
        String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        final DataBusAppInfo appInfo = new DataBusAppInfo();
        appInfo.setAppId(appId);
        appInfo.setMerchantId(merchantId);
        return appInfo;
    }

    @Override
    public SyncSubMchToLklResp syncSubMchToLkl(String providerMerchantId) {
        // 2.同步信息到lkl
        Map<String, com.wosai.upay.merchant.contract.model.ContractResponse> syncResult = lklV3Service.syncSubMchByMerCupNo(providerMerchantId);
        // 3.返回同步结果
        String wxMsg = BeanUtil.getPropString(syncResult, "wx.message", "");
        String alipayMsg = BeanUtil.getPropString(syncResult, "alipay.message", "");
        return new SyncSubMchToLklResp()
                .setWx(WosaiStringUtils.isEmpty(wxMsg) ? "微信子商户号不存在" : wxMsg)
                .setAli(WosaiStringUtils.isEmpty(alipayMsg) ? "支付宝子商户号不存在" : alipayMsg);
    }

    @Override
    public UnionPayOpenStatusQueryResp queryUnionPayOpenStatus(UnionPayOpenStatusQueryReq req) {
        Map merchant = merchantService.getMerchantByMerchantId(req.getMerchantId());
        String merchantSn = WosaiMapUtils.getString(merchant, Merchant.SN);
        // 还没有入网成功，就展示这个
        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        if (Objects.isNull(contractStatus) || ContractStatus.STATUS_PENDING == contractStatus.getStatus()) {
            return UnionPayOpenStatusQueryResp.builder()
                    .status(UnionPayOpenStatusQueryResp.NOT_OPEN)
                    .message("开通中，请稍后重试")
                    .retry(false)
                    .build();
        }
        String acquirer = WosaiStringUtils.isEmpty(req.getAcquirer()) ? contractStatus.getAcquirer() : req.getAcquirer();
        UnionPayOpenStatusQueryResp unionPayOpenStatusQueryResp = composeAcquirerBiz.queryUnionPayOpenStatus(merchantSn, acquirer);
        if (WosaiStringUtils.isNotEmpty(unionPayOpenStatusQueryResp.getMessage()) && unionPayOpenStatusQueryResp.getStatus().equals(UnionPayOpenStatusQueryResp.FAIL_OPEN)) {
            ErrorInfo errorInfo = transMemo(unionPayOpenStatusQueryResp.getMessage(), req.getPlatform());
            unionPayOpenStatusQueryResp.setFallBack(errorInfo.getFallBack());
            unionPayOpenStatusQueryResp.setMessage(errorInfo.getMsg());
        }
        return unionPayOpenStatusQueryResp;
    }

    @Override
    public UnionPayOpenStatusQueryResp queryUnionPayOpenStatusForCrm(ApiRequestParam<UnionPayOpenStatusQueryReq> req) {
        UnionPayOpenStatusQueryReq bodyParams = req.getBodyParams();
        if (Objects.isNull(bodyParams.getPlatform()) || WosaiStringUtils.isEmpty(bodyParams.getMerchantId())) {
            throw new CommonInvalidParameterException("商户ID或来源平台不能为空");
        }
        return queryUnionPayOpenStatus(bodyParams);
    }

    @Override
    public CommonResult changePaymentMode(PaymentModeChangeReq req) {
        return paymentModeChangeBiz.changePaymentMode(req);
    }

    /**
     * 对云闪付状态进行文案转译
     *
     * @param sourceMessage 源文案
     * @param platform      来源平台
     * @return
     */
    private ErrorInfo transMemo(String sourceMessage, PlatformEnum platform) {
        final ErrorInfo errorInfo = errorCodeManageBiz.getPromptMessageFromErrorCodeManager(platform.getValue(), sourceMessage, ErrorCodeManageBiz.PLATFORM_UNION_PAY);
        return errorInfo;
    }

}
