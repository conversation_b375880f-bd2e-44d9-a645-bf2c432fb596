package com.wosai.upay.job.service;


import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Table;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.DisableStatusEnum;
import com.shouqianba.cua.utils.json.JSON;
import com.shouqianba.model.dto.response.DisableAcquirerTerminalRspDTO;
import com.shouqianba.service.TerminalManagementService;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.model.StoreInfo;
import com.wosai.mc.service.MerchantService;
import com.wosai.mc.service.StoreService;
import com.wosai.upay.core.model.Terminal;
import com.wosai.upay.core.service.TerminalService;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.ProviderTerminalBiz;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.mapper.ProviderTerminalBindConfigMapper;
import com.wosai.upay.job.mapper.ProviderTerminalMapper;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.refactor.dao.McAcquirerDAO;
import com.wosai.upay.job.refactor.dao.McProviderDAO;
import com.wosai.upay.job.refactor.dao.ProviderTerminalBindConfigDAO;
import com.wosai.upay.job.refactor.dao.ProviderTerminalDAO;
import com.wosai.upay.job.refactor.model.entity.McProviderDO;
import com.wosai.upay.job.refactor.model.entity.ProviderTerminalBindConfigDO;
import com.wosai.upay.job.refactor.model.entity.ProviderTerminalDO;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.fuyou.response.TermQueryResponse;
import com.wosai.upay.merchant.contract.service.FuyouService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@AutoJsonRpcServiceImpl
@Slf4j
public class ProviderTerminalSerivceImpl implements ProviderTerminalSerivce {

    @Autowired
    private ProviderTerminalBiz providerTerminalBiz;

    @Autowired
    RuleContext ruleContext;

    @Autowired
    private ProviderTerminalBindConfigMapper configMapper;

    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Autowired
    private TerminalService terminalService;
    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private StoreService storeService;

    @Autowired
    private ProviderTerminalMapper providerTerminalMapper;

    @Autowired
    private McProviderDAO mcProviderDAO;
    @Autowired
    private McAcquirerDAO mcAcquirerDAO;

    @Autowired
    private FuyouService fuyouService;

    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;
    @Autowired
    private ProviderTerminalDAO providerTerminalDAO;
    @Autowired
    private ProviderTerminalBindConfigDAO providerTerminalBindConfigDAO;
    @Autowired
    @Qualifier("updateProviderTerminalThreadPoolTaskExecutor")
    private ThreadPoolTaskExecutor updateProviderTerminalThreadPoolTaskExecutor;

    @Resource(type = TerminalManagementService.class)
    private TerminalManagementService terminalManagementService;


    @Override
    public void updateProviderTermianl(Map store) {
        updateProviderTerminalThreadPoolTaskExecutor.execute(() -> providerTerminalBiz.updateTerminalByStoreSn(BeanUtil.getPropString(store, "sn")));
    }

    @Override
    public Map<String, Object> findBindConfigs(ProviderTerminalBindConfigFindReq req) {
        if (WosaiStringUtils.isEmpty(req.getMerchantSn()) && WosaiStringUtils.isEmpty(req.getStoreSn()) && WosaiStringUtils.isEmpty(req.getTerminalSn()) && WosaiStringUtils.isEmpty(req.getProviderTerminalId())) {
            return CollectionUtil.hashMap("records", new ArrayList<>());
        }
        ProviderTerminalBindConfig example = new ProviderTerminalBindConfig();
        if (WosaiStringUtils.isNotEmpty(req.getMerchantSn())) {
            example.setMerchant_sn(req.getMerchantSn());
        }
        if (WosaiStringUtils.isNotEmpty(req.getStoreSn())) {
            example.setStore_sn(req.getStoreSn());
        }
        if (WosaiStringUtils.isNotEmpty(req.getTerminalSn())) {
            example.setTerminal_sn(req.getTerminalSn());
        }
        if (WosaiStringUtils.isNotEmpty(req.getProviderTerminalId())) {
            example.setProvider_terminal_id(req.getProviderTerminalId());
        }
        if (WosaiStringUtils.isNotEmpty(req.getSubMchId())) {
            example.setSub_mch_id(req.getSubMchId());
        }
        if (cn.hutool.core.collection.CollectionUtil.isNotEmpty(req.getSubMchIds())) {
            example.setSub_mch_ids(req.getSubMchIds());
        }
        PageHelper.startPage(1, 1000);
        List<ProviderTerminalBindConfig> configs = getConfigs(example);
        List<ProviderTerminalBindConfig> fuyouConfigs = buildFuyouProviderTerminalConfigResp(req.getMerchantSn(), req.getStoreSn(), req.getTerminalSn(), req.getProviderTerminalId());
        if (fuyouConfigs != null && fuyouConfigs.size() > 0){
            configs.addAll(fuyouConfigs);
        }
        List<ProviderTerminalBindConfigResp> providerTerminalBindConfigResp = fillAcquirer(configs);
        try {
            populateDisableReasons(providerTerminalBindConfigResp);
        } catch (Exception ex) {
            log.error("populateDisableReasons error, req:{}", JSON.toJSONString(req), ex);
        }
        return CollectionUtil.hashMap("records", providerTerminalBindConfigResp);
    }


    private List<ProviderTerminalBindConfig> buildFuyouProviderTerminalConfigResp(String merchantSn, String storeSn, String terminalSn, String providerTerminalId){
        //商户在收单机构的商户号
        ProviderTerminal terminal = new ProviderTerminal();
        if (WosaiStringUtils.isNotEmpty(merchantSn)) {
            terminal.setMerchant_sn(merchantSn);
        }
        if (WosaiStringUtils.isNotEmpty(storeSn)) {
            terminal.setStore_sn(storeSn);
        }
        if (WosaiStringUtils.isNotEmpty(terminalSn)) {
            terminal.setTerminal_sn(terminalSn);
        }
        if (WosaiStringUtils.isNotEmpty(providerTerminalId)) {
            terminal.setProvider_terminal_id(providerTerminalId);
        }
        terminal.setProvider(ProviderEnum.PROVIDER_FUYOU.getValue());
        List<ProviderTerminal> providerTerminals = providerTerminalMapper.selectByCondition(terminal);
        if (CollectionUtils.isEmpty(providerTerminals)){
            return null;
        }
        List<ContractSubTask> contractSubTaskList = contractSubTaskMapper.getFuyouTerminalSubTask(providerTerminals.get(0).getMerchant_sn());
        if (contractSubTaskList == null || contractSubTaskList.size() == 0){
            return null;
        }
        List<ProviderTerminalBindConfig> configs = new ArrayList<>();
        for (ProviderTerminal providerTerminal : providerTerminals){
            ProviderTerminalBindConfig config = new ProviderTerminalBindConfig(providerTerminal);
            for (ContractSubTask subTask : contractSubTaskList){
                if (BeanUtil.getPropString(subTask.getResponseBody() , "fy_term_id", "").contains(providerTerminal.getProvider_terminal_id())){
                    config.setRequest_body(subTask.getRequest_body());
                    config.setResponse_body(subTask.getResponse_body());
                    config.setCreate_at(subTask.getCreate_at());
                    config.setUpdate_at(subTask.getUpdate_at());
                    config.setResult(subTask.getResult());
                    if (subTask.getStatus() == 5){
                        config.setStatus(1);
                    }else {
                        config.setStatus(2);
                    }
                    break;
                }
            }
            configs.add(config);
        }
        return configs;
    }

    private void populateDisableReasons(List<ProviderTerminalBindConfigResp> configs) {
        Optional<String> merchantSnOpt = configs.stream().map(ProviderTerminalBindConfigResp::getMerchant_sn).filter(StringUtils::isNotBlank).findAny();
        Set<String> terminalNos = configs.stream().map(ProviderTerminalBindConfigResp::getProvider_terminal_id).collect(Collectors.toSet());
        if (!merchantSnOpt.isPresent() || CollectionUtils.isEmpty(terminalNos)) {
            return;
        }
        List<DisableAcquirerTerminalRspDTO> disableAcquirerTerminalRspDTOS = terminalManagementService
                .batchListMerchantAcquirerTerminalNoDisableReasons(merchantSnOpt.get(), new ArrayList<>(terminalNos));
        Table<String/*acquirerTerminalNo*/, String/*acquirer*/, DisableAcquirerTerminalRspDTO> terminalTable = HashBasedTable.create();
        for (DisableAcquirerTerminalRspDTO dto : disableAcquirerTerminalRspDTOS) {
            String acquirerTerminalNo = dto.getAcquirerTerminalNo();
            String acquirer = dto.getAcquirer();
            terminalTable.put(acquirerTerminalNo, acquirer, dto);
        }
        for (ProviderTerminalBindConfigResp config : configs) {
            populateDisableReason(config, terminalTable);
        }
    }

    private void populateDisableReason(ProviderTerminalBindConfigResp config, Table<String, String, DisableAcquirerTerminalRspDTO> terminalTable) {
        String providerTerminalId = config.getProvider_terminal_id();
        Integer provider = config.getProvider();
        Optional<McProviderDO> providerOpt = mcProviderDAO.getByProvider(String.valueOf(provider));
        if (!providerOpt.isPresent()) {
            return;
        }
        McProviderDO mcProviderDO = providerOpt.get();
        String acquirer = mcProviderDO.getAcquirer();
        config.setDisableStatus(DisableStatusEnum.ACTIVATE.getValue());
        if (terminalTable.rowKeySet().contains(providerTerminalId)) {
            DisableAcquirerTerminalRspDTO disableAcquirerDTO = terminalTable.row(providerTerminalId).get(acquirer);
            if (Objects.isNull(disableAcquirerDTO)) {
                return;
            }
            String disableReasons = disableAcquirerDTO.getDisableReasons();
            if (StringUtils.isBlank(disableReasons)) {
                return;
            }
            List<Map> maps = JSON.parseArray(disableReasons, Map.class);
            String disableReason = maps.stream().map(new Function<Map, String>() {
                @Override
                public String apply(Map map) {
                    return MapUtils.getString(map, "disableReason", "");
                }
            }).filter(org.apache.commons.lang3.StringUtils::isNotBlank).collect(Collectors.joining(","));
            config.setDisableStatus(DisableStatusEnum.DISABLE.getValue());
            config.setDisableReason(disableReason);
        }
    }

    @Override
    public List<ProviderTerminalBindConfigResp> getBindConfigsByMerchantSn(String merchantSn) {
        ProviderTerminalBindConfig example = new ProviderTerminalBindConfig().setMerchant_sn(merchantSn);
        List<ProviderTerminalBindConfig> configs = getConfigs(example);
        configs.addAll(buildFuyouProviderTerminalConfigResp(merchantSn, null, null, null));
        return fillAcquirer(configs);
    }

    @Override
    public List<ProviderTerminalBindConfigResp> getBindConfigsByStoreSn(String storeSn) {
        ProviderTerminalBindConfig example = new ProviderTerminalBindConfig().setStore_sn(storeSn);
        List<ProviderTerminalBindConfig> configs = getConfigs(example);
        configs.addAll(buildFuyouProviderTerminalConfigResp(null, storeSn, null, null));
        return fillAcquirer(configs);
    }

    @Override
    public List<ProviderTerminalBindConfigResp> getBindConfigsByTerminalSn(String terminalSn) {
        ProviderTerminalBindConfig example = new ProviderTerminalBindConfig().setTerminal_sn(terminalSn);
        List<ProviderTerminalBindConfig> configs = getConfigs(example);
        configs.addAll(buildFuyouProviderTerminalConfigResp(null, null, terminalSn, null));
        return fillAcquirer(configs);
    }

    private List<ProviderTerminalBindConfigResp> fillAcquirer(List<ProviderTerminalBindConfig> configs) {
        List<ProviderTerminalBindConfigResp> result = new ArrayList<>();
        for (ProviderTerminalBindConfig config : configs) {
            Optional<McProviderDO> mcProviderDO = mcProviderDAO.getByProvider(String.valueOf(config.getProvider()));
            String acquirer = mcProviderDO.isPresent() ? mcProviderDO.get().getAcquirer() : WosaiStringUtils.EMPTY;
            ProviderTerminalBindConfigResp resp = new ProviderTerminalBindConfigResp();
            resp.setAssociateType(WosaiStringUtils.isNotEmpty(config.getTerminal_sn()) ? "终端" : WosaiStringUtils.isNotEmpty(config.getStore_sn()) ? "门店" : "商户");
            resp.setAssociateSn(WosaiStringUtils.isNotEmpty(config.getTerminal_sn()) ? config.getTerminal_sn() : WosaiStringUtils.isNotEmpty(config.getStore_sn()) ? config.getStore_sn() : config.getMerchant_sn());
            resp.setAcquirer(acquirer);
            resp.setAcquirerName(mcAcquirerDAO.getAcquirerName(acquirer));
            if (config.getCreate_at() != null) {
                resp.setCreateAtTimestamp(config.getCreate_at().getTime());
            }
            resp.setId(config.getId());
            resp.setMerchant_sn(config.getMerchant_sn());
            resp.setStore_sn(config.getStore_sn());
            resp.setTerminal_sn(config.getTerminal_sn());
            resp.setProvider(config.getProvider());
            resp.setPayway(config.getPayway());
            resp.setSub_mch_id(config.getSub_mch_id());
            resp.setProvider_terminal_id(config.getProvider_terminal_id());
            resp.setStatus(config.getStatus());
            resp.setResult(config.getResult());
            resp.setRequest_body(config.getRequest_body());
            resp.setResponse_body(config.getResponse_body());
            resp.setCreate_at(config.getCreate_at());
            resp.setUpdate_at(config.getUpdate_at());

            result.add(resp);
        }
        return result;
    }

    @Override
    public ProviderTerminalBindConfig syncAcquireBindConfig(String id) {
        ProviderTerminalBindConfig config = configMapper.selectByPrimaryKey(id);
        if (Objects.isNull(config)) {
            return null;
        }
        Integer payway = config.getPayway();
        if (!PaywayEnum.UNIONPAY.getValue().equals(payway) && !PaywayEnum.BANK_CARD.getValue().equals(payway)) {
            return null;
        }
        if(PaywayEnum.BANK_CARD.getValue().equals(payway) && Objects.equals(config.getProvider(), ProviderEnum.PROVIDER_TONGLIAN_V2.getValue())) {
            return null;
        }
        MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(config.getMerchant_sn(), ProviderEnum.PROVIDER_LAKALA_V3.getValue());
        if (Objects.isNull(acquirerParams)) {
            return null;
        }
        providerTerminalBiz.syncTermResultQuery(acquirerParams, config);
        return configMapper.selectByPrimaryKey(config.getId());
    }

    private List<ProviderTerminalBindConfig> getConfigs(ProviderTerminalBindConfig example) {
        List<ProviderTerminalBindConfig> configs = configMapper.selectByExample(example);
        configs.sort(Comparator.comparing(ProviderTerminalBindConfig::getCreate_at).reversed());
        return configs;
    }


    /**
     * 终端对应的在收单机构的终端号
     * @param merchantSn
     * @param storeSn
     * @param terminalSn
     * @param vendorAppAppid
     * @return
     */
    public ProviderTerminal getProviderTerminalId(String merchantSn, String storeSn, String terminalSn, String  vendorAppAppid) {
        ProviderTerminal terminal = new ProviderTerminal();
        terminal.setMerchant_sn(merchantSn);
        terminal.setStore_sn(storeSn);
        terminal.setTerminal_sn(terminalSn);
        terminal.setTerminal_appid(vendorAppAppid);
        terminal.setProvider(ProviderEnum.PROVIDER_LAKALA_V3.getValue());
        List<ProviderTerminal> providerTerminals = providerTerminalMapper.selectByCondition(terminal);
        if(CollectionUtils.isEmpty(providerTerminals)) {
            return null;
        }
        ProviderTerminal providerTerminal = Optional.ofNullable(providerTerminals.get(0))
                .orElseGet(ProviderTerminal::new);
        return providerTerminal;
    }

    @Override
    public void updateT9TerminalInfo(String termId) {
        Map terminal = Optional.ofNullable(terminalService.getTerminalByTerminalId(termId))
                .orElseGet(HashMap::new);
        String vendorAppAppid = BeanUtil.getPropString(terminal, Terminal.VENDOR_APP_APPID);
        //智能posT9标识
        List<String> simpleSuperPosList = Lists.newArrayList(applicationApolloConfig.getSimpleSuperPos());
        if(!simpleSuperPosList.contains(vendorAppAppid)) {
            return;
        }
        String storeId = BeanUtil.getPropString(terminal, Terminal.STORE_ID);
        String terminalSn = BeanUtil.getPropString(terminal, Terminal.SN);
        StoreInfo storeInfo = storeService.getStoreById(storeId, null);
        String merchantId = BeanUtil.getPropString(terminal, Terminal.MERCHANT_ID);
        MerchantInfo merchant = merchantService.getMerchantById(merchantId, null);
        if(Objects.isNull(merchant) || Objects.isNull(storeInfo)) {
            return;
        }
        String merchantSn = merchant.getSn();
        ProviderTerminal providerTerminal = getProviderTerminalId(merchantSn, storeInfo.getSn(), terminalSn, vendorAppAppid);
        String providerTerminalId = providerTerminal.getProvider_terminal_id();
        if(Objects.isNull(providerTerminal) || StringUtils.isEmpty(providerTerminalId)) {
            return;
        }
        //更新在支付宝和微信侧的状态
        providerTerminalBiz.addOrUpdateTerm(providerTerminal);
        //更新交易信息
        providerTerminalBiz.createOrUpdateTerminalExtra(merchantSn,
                providerTerminalId,
                ProviderEnum.PROVIDER_LAKALA_V3.getValue(),
                vendorAppAppid,
                terminalSn
                );
    }

    @Override
    public void restoreT9TerminalInfo(String termId) {
        Map terminal = Optional.ofNullable(terminalService.getTerminalByTerminalId(termId))
                .orElseGet(HashMap::new);
        String vendorAppAppid = BeanUtil.getPropString(terminal, Terminal.VENDOR_APP_APPID);
        //智能posT9标识
        List<String> simpleSuperPosList = Lists.newArrayList(applicationApolloConfig.getSimpleSuperPos());
        if(!simpleSuperPosList.contains(vendorAppAppid)) {
            return;
        }
        String storeId = BeanUtil.getPropString(terminal, Terminal.STORE_ID);
        String terminalSn = BeanUtil.getPropString(terminal, Terminal.SN);
        StoreInfo storeInfo = storeService.getStoreById(storeId, null);
        String merchantId = BeanUtil.getPropString(terminal, Terminal.MERCHANT_ID);
        MerchantInfo merchant = merchantService.getMerchantById(merchantId, null);
        if(Objects.isNull(merchant) || Objects.isNull(storeInfo)) {
            return;
        }
        String merchantSn = merchant.getSn();
        ProviderTerminal providerTerminal = getProviderTerminalId(merchantSn, storeInfo.getSn(), terminalSn, vendorAppAppid);
        String providerTerminalId = providerTerminal.getProvider_terminal_id();
        if(Objects.isNull(providerTerminal) || StringUtils.isEmpty(providerTerminalId)) {
            return;
        }
        //重新设置类型为11
        providerTerminalBiz.restoreT9TerminalInfo(providerTerminal);
        //更新交易信息
        providerTerminalBiz.deleteContentTermInfo(merchantSn,
                ProviderEnum.PROVIDER_LAKALA_V3.getValue(),
                terminalSn
        );
    }

    @Override
    public void updateFuYouT9TerminalInfo(String termId) {
        Map terminal = Optional.ofNullable(terminalService.getTerminalByTerminalId(termId))
                .orElseGet(HashMap::new);
        String vendorAppAppid = BeanUtil.getPropString(terminal, Terminal.VENDOR_APP_APPID);
        //富友智能posT9标识
        List<String> fuYouSuperPosList = Lists.newArrayList(applicationApolloConfig.getFySimpleSuperPos());
        if(!fuYouSuperPosList.contains(vendorAppAppid)) {
            return;
        }
        String storeId = BeanUtil.getPropString(terminal, Terminal.STORE_ID);
        String terminalSn = BeanUtil.getPropString(terminal, Terminal.SN);
        StoreInfo storeInfo = storeService.getStoreById(storeId, null);
        String merchantId = BeanUtil.getPropString(terminal, Terminal.MERCHANT_ID);
        MerchantInfo merchant = merchantService.getMerchantById(merchantId, null);
        if(Objects.isNull(merchant) || Objects.isNull(storeInfo)) {
            return;
        }
        String merchantSn = merchant.getSn();
        ContractResponse response = fuyouService.termQuery(merchantSn, terminalSn);
        if(!response.isSuccess()){
            // 如果富友服务调用失败打印日志
            log.warn("查询富友刷卡设备信息异常 商户号:{},termId:{},terminalSn:{},error:{}",merchantSn,termId,terminalSn,response.getMessage());
        }
        // 处理富友服务返回的响应数据
        Map<String, Object> respData = Optional.ofNullable(response.getResponseParam()).orElseGet(HashMap::new);
        TermQueryResponse termQueryResponse = JSONObject.parseObject(JSONObject.toJSONString(respData), TermQueryResponse.class);
        String acquireTermId = termQueryResponse.getTermId();
        if(StringUtils.isEmpty(acquireTermId)) {
            // 如果不存在，打印日志
            log.warn("富友刷卡设备业务信息异常没有终端Id 商户号:{},termId:{},terminalSn:{}",merchantSn,termId,terminalSn);

        }
        //更新交易信息
        providerTerminalBiz.createOrUpdateTerminalExtra(merchantSn,
                acquireTermId,
                PayParamsModel.PROVIDER_FUYOU,
                BeanUtil.getPropString(terminal,Terminal.VENDOR_APP_APPID),
                terminalSn);
    }

    @Override
    public void deleteTerminalLevelProviderTerminal(ProviderTerminalDeleteReq req) {
        providerTerminalDAO.deleteTerminalLevelProviderTerminal(req.getMerchantSn(), req.getProvider());
        providerTerminalBindConfigDAO.deleteTerminalLevelProviderTerminalBindConfig(req.getMerchantSn(), req.getProvider());
    }
}
