package com.wosai.upay.job.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.FuYouContractSettleTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.BankAccountTypeEnum;
import com.shouqianba.cua.enums.core.BusinessLicenseTypeEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.workflow.bean.dto.AuditPreStartDto;
import com.shouqianba.workflow.service.AuditService;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.databus.event.merchant.contract.MerchantContractWeixinAuthEvent;
import com.wosai.mpay.util.StringUtils;
import com.wosai.profit.sharing.model.request.OpenFuYouSharingReq;
import com.wosai.profit.sharing.service.SharingOpenService;
import com.wosai.sales.todo.service.TodoDetailService;
import com.wosai.sales.todo.vo.Creator;
import com.wosai.sales.todo.vo.Executor;
import com.wosai.sales.todo.vo.LinkObject;
import com.wosai.sales.todo.vo.TodoAddReq;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.Constants.MerchantChangeDataConstant;
import com.wosai.upay.job.biz.*;
import com.wosai.upay.job.biz.acquirer.FeeRateBiz;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.constant.ContractRuleConstants;
import com.wosai.upay.job.enume.ScheduleEnum;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.*;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.model.DO.MultiProviderContractEvent;
import com.wosai.upay.job.providers.BasicProvider;
import com.wosai.upay.job.providers.FuyouProvider;
import com.wosai.upay.job.providers.ProviderFactory;
import com.wosai.upay.job.refactor.biz.acquirer.fuyou.FuYouAcquirerFacade;
import com.wosai.upay.job.refactor.dao.McChannelDAO;
import com.wosai.upay.job.refactor.model.entity.McChannelDO;
import com.wosai.upay.job.scheduler.ScheduleUtil;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.DefaultValueUtil;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.util.StringUtil;
import entity.common.UserEs;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;


/**
 * @Author: jerry
 * @date: 2019/8/30 11:50
 * @Description:修改任务状态
 */
@Service
@Slf4j
public class TaskResultServiceImpl implements TaskResultService {


    @Autowired
    ContractTaskMapper contractTaskMapper;

    @Autowired
    private MultiProviderContractEventMapper multiEventMapper;

    @Autowired
    private ContractStatusMapper contractStatusMapper;

    @Autowired
    private PayWayConfigChangeMapper payWayConfigChangeMapper;

    @Autowired
    DataBusBiz dataBusBiz;
    @Autowired
    BankCardServiceImpl bankCardService;

    @Autowired
    private MultiEventBiz multiEventBiz;

    @Autowired
    private UpdateTradeParamsBiz updateTradeParamsBiz;

    @Autowired
    WeixinAuthApplyBiz weixinAuthApplyBiz;

    @Autowired
    private RuleContext ruleContext;

    @Autowired
    private ProviderFactory providerFactory;

    @Autowired
    ContractSubTaskMapper contractSubTaskMapper;

    @Autowired
    private ContractTaskBiz contractTaskBiz;

    @Autowired
    private ContractSubTaskBiz contractSubTaskBiz;

    @Autowired
    PendingTasksBiz pendingTasksBiz;

    @Autowired
    private BackAcquirerBiz backAcquirerBiz;

    @Autowired
    private MerchantChangeDataBiz merchantChangeDataBiz;

    @Autowired
    private SharingOpenService sharingOpenService;

    @Autowired
    private BankDirectApplyMapper bankDirectApplyMapper;

    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Autowired
    private TodoDetailService todoDetailService;

    @Autowired
    private AopBiz aopBiz;

    @Autowired
    private McChannelDAO mcChannelDAO;

    @Autowired
    private ChatBotUtil chatBotUtil;

    @Autowired
    private AuditService auditService;


    @Value("${fuyou.approval.templateId}")
    private String fuyouApprovalTemplateId;

    @Autowired
    MerchantService merchantService;

    @Autowired
    FuyouProvider fuyouProvider;

    @Autowired
    FeeRateBiz feeRateBiz;

    @Autowired
    BlackListBiz blackListBiz;

    @Autowired
    private ScheduleUtil scheduleUtil;

    @Resource
    private FuYouAcquirerFacade fuYouAcquirerFacade;

    public static List<Integer> BELONG_LKLV3 = Lists.newArrayList(ProviderEnum.PROVIDER_DIRECT_UNIONPAY.getValue(), ProviderEnum.PROVIDER_LKLWANMA.getValue(),
            ProviderEnum.PROVIDER_LKLORG.getValue(), ProviderEnum.PROVIDER_UION_OPEN.getValue(), ProviderEnum.PROVIDER_LKL_OPEN.getValue());

    public static List<String> NEED_QUERY_TASK_TYPES = Lists.newArrayList(
            ProviderUtil.CONTRACT_TYPE_UPDATE_BANK_ACCOUNT,
            ProviderUtil.CONTRACT_TYPE_UPDATE_BASIC,
            ProviderUtil.CONTRACT_TYPE_UPDATE_BUSINESS_LICENSE,
            ProviderUtil.MERCHANT_BUSINESS_DATA_CHANGE
    );
    public static List<String> SUPPORT_SYNC_UNION_PAY_ACQUIRER = Lists.newArrayList(
            AcquirerTypeEnum.LKL_V3.getValue(),
            AcquirerTypeEnum.HAI_KE.getValue(),
            AcquirerTypeEnum.FU_YOU.getValue()
    );


    @Override
    public void changeStatusAndResultV2(Long taskId, Long subTaskId, int resultStatus, String result, boolean authPass) {
        ContractTask task = contractTaskMapper.selectByPrimaryKey(taskId);
        backAcquirerBiz.handleContractTask(task, resultStatus);
        String merchantSn = task.getMerchant_sn();
        final ContractSubTask contractSubTask = contractSubTaskMapper.selectByPrimaryKey(subTaskId);

        //小微升级新增商户入网,由于当前不会将参数存储在merchant_provider_params表,所以暂时不能处理终端等其他信息,只保证商户入网成功
        if(ProviderUtil.CONTRACT_TYPE_INSERT.equals(task.getType()) &&
                StrUtil.contains(task.getRule_group_id(), ContractRuleConstants.RULE_GROUP_MICROUPGRADE)) {

            ContractTask update = new ContractTask()
                    .setId(taskId)
                    .setStatus(resultStatus);
            if (!StringUtil.empty(result)) {
                update.setResult(result);
            }
            //更新contract_task表status,并根据type和status判断是否发送消息到神策
            contractTaskBiz.update(update);
            return;
        }

        //259文件2期,该商户下创建门店级别终端和收钱吧终端对应的终端
        if (ProviderUtil.CONTRACT_TYPE_INSERT.equals(task.getType()) && resultStatus == TaskStatus.SUCCESS.getVal()) {
            final String contractRule = contractSubTask.getContract_rule();
            if (StringUtils.isEmpty(contractRule)) {
                return;
            }
            final ContractRule rule = ruleContext.getContractRule(contractRule);
            // 富友进件成功后，触发资金归集
            if (ProviderEnum.PROVIDER_FUYOU.getValue().equals(Integer.valueOf(rule.getProvider()))){
                try{
                    fuyouProvider.doCreateProviderTerminal(merchantSn, ProviderEnum.PROVIDER_FUYOU.getValue());
                    checkAndSendTodo(task.getEventContext());
                }catch (Exception e){
                    log.error("发送富友补充对公凭证待办失败,{},{},{}", taskId, subTaskId, resultStatus);
                    chatBotUtil.sendMessageToContractWarnChatBot(String.format("发送富友补充对公凭证待办失败,商户SN:%s,终端号:%s,原因:%s", taskId, subTaskId, "发送富友补充对公凭证待办失败"));
                }
                try {
                    log.info("触发fuyou资金归集,{},{},{}", taskId, subTaskId, resultStatus);
                    MerchantProviderParamsExample example = new MerchantProviderParamsExample();
                    example.or().andMerchant_snEqualTo(merchantSn)
                            .andProviderEqualTo(ProviderEnum.PROVIDER_FUYOU.getValue())
                            .andPaywayEqualTo(PaywayEnum.ACQUIRER.getValue())
                            .andDeletedEqualTo(false);
                    example.setOrderByClause("ctime desc");
                    List<MerchantProviderParams> merchantProviderParams = merchantProviderParamsMapper.selectByExampleWithBLOBs(example);
                    if (merchantProviderParams != null && merchantProviderParams.size() > 0) {
                        OpenFuYouSharingReq req = new OpenFuYouSharingReq();
                        req.setPrivateMchId(merchantProviderParams.get(0).getProvider_merchant_id());
                        String settleType = fuYouAcquirerFacade.getContractSettleTypeByMerchantSn(task.getMerchant_sn());
                        if (!Objects.equals(FuYouContractSettleTypeEnum.MANUAL_SETTLEMENT_D1.getValue().toString(), settleType)) {
                            sharingOpenService.openFuYouSharing(req);
                        }
                    }
                } catch (Exception e) {
                    log.error("触发fuyou资金归集异常,{},{},{}", taskId, subTaskId, resultStatus);
                    chatBotUtil.sendMessageToContractWarnChatBot(String.format("触发富友资金归集异常,商户SN:%s,终端号:%s,原因:%s", taskId, subTaskId, "触发富友资金归集失败"));
                }
            }
            //兼容银联万码
            if (BELONG_LKLV3.contains(rule.getProvider())) {
                rule.setProvider(String.valueOf(ProviderEnum.PROVIDER_LAKALA_V3.getValue()));
            }
            BasicProvider provider = providerFactory.getProviderByContractRule(rule);
            log.info("商户:{}进件成功,开始同步终端和门店,provider:{}", merchantSn, rule.getProvider());
            CompletableFuture.runAsync(() -> provider.createProviderTerminal(merchantSn, Integer.valueOf(rule.getProvider())));
        }
        if (resultStatus == TaskStatus.FAIL.getVal()){
            final String contractRule = contractSubTask.getContract_rule();
            if (!StringUtils.isEmpty(contractRule)) {
                final ContractRule rule = ruleContext.getContractRule(contractRule);
                blackListBiz.createRiskOrder(merchantSn, task.getType(), rule.getAcquirer(), MapUtils.getString(JSON.parseObject(result, Map.class), "result"));
            }
        }
        // 如果是入网任务  && 任务状态为成功或失败
        if (ProviderUtil.CONTRACT_TYPE_INSERT.equals(task.getType()) && TaskStatus.isFinish(resultStatus)) {
            MultiProviderContractEvent contractEvent = multiEventMapper.selectMultiEventByMerchantSnAndTaskId(merchantSn, taskId);
            // 多通道进件事件不为空
            if (contractEvent != null) {
                handleContractTaskByMultiEvent(task, resultStatus, result, contractEvent);
                return;
            }
        }
        // 费率变更任务，发送kafka消息通知交易中心结果
        if (ProviderUtil.CONTRACT_TYPE_UPDATE_FEERATE.equals(task.getType()) && TaskStatus.isFinish(resultStatus)) {
            final String contractRule = contractSubTask.getContract_rule();
            if (!StringUtils.isEmpty(contractRule)) {
                final ContractRule rule = ruleContext.getContractRule(contractRule);
                feeRateBiz.sendFeeRateKafkaMsg(rule.getProvider() , task.getMerchant_sn(), task, TaskStatus.SUCCESS.getVal().equals(resultStatus));
            }
        }
        //成功或失败 写数据总线 修改银行卡状态
        if (TaskStatus.isFinish(resultStatus)) {
            String memo = resultStatus == TaskStatus.FAIL.getVal() ? MapUtils.getString(JSON.parseObject(result, Map.class), "result") : "SUCCESS";
            if (ProviderUtil.CONTRACT_TYPE_INSERT.equals(task.getType())) {
                dataBusBiz.insert(resultStatus == TaskStatus.SUCCESS.getVal() ? ContractStatus.STATUS_SUCCESS : ContractStatus.STATUS_BIZ_FAIL, merchantSn, memo);
            }
            if (ProviderUtil.MERCHANT_BUSINESS_DATA_CHANGE.equals(task.getType())) {
                Map event_context = JSON.parseObject(task.getEvent_context(), Map.class);
                Map merchant = (Map) event_context.get(ParamContextBiz.KEY_MERCHANT);
                merchantChangeDataBiz.sendMerchantChangeDataStatusChange(merchantSn, (String) merchant.get(DaoConstants.ID), MerchantChangeDataConstant.COMMON_STATUS_PROCESS,
                        resultStatus == TaskStatus.SUCCESS.getVal() ? MerchantChangeDataConstant.COMMON_STATUS_SUCCESS : MerchantChangeDataConstant.COMMON_STATUS_FAIL, memo,
                        MapUtils.getString(event_context, MerchantChangeDataConstant.APPLY_SOURCE, ""), MapUtils.getBooleanValue(event_context, MerchantChangeDataConstant.RE_CONTRACT, false));
            }
            if (ProviderUtil.CONTRACT_TYPE_AUTH.equals(task.getType()) && resultStatus == TaskStatus.FAIL.getVal()) {
                MerchantContractWeixinAuthEvent event = new MerchantContractWeixinAuthEvent();
                event.setAuthStatus(0);
                event.setMerchantSn(merchantSn);
                event.setNeedAuth(false);
                String merchantID = (String) ((Map) JSON.parseObject(task.getEvent_context(), Map.class).get(ParamContextBiz.KEY_MERCHANT)).get(DaoConstants.ID);
                event.setMerchantId(merchantID);
                dataBusBiz.insertWeixinAuthEvent(event);
            }
            if (contractSubTaskBiz.changeBankStatusById(taskId, subTaskId)) {
                Map context = JSON.parseObject(task.getEvent_context(), Map.class);
                bankCardService.updateCardAfterTaskStatus(context, resultStatus == TaskStatus.SUCCESS.getVal() ? MerchantBankAccount.VERIFY_STATUS_SUCC : MerchantBankAccount.VERIFY_STATUS_FAIL, memo);
            }
        }
        if (NEED_QUERY_TASK_TYPES.contains(task.getType()) && TaskStatus.SUCCESS.getVal().equals(resultStatus)) {
            Optional<McChannelDO> mcChannel = mcChannelDAO.getMcChannelByChannel(contractSubTask.getChannel());
            if (mcChannel.isPresent()){
                if (AcquirerTypeEnum.FU_YOU.getValue().equals(mcChannel.get().getAcquirer()) && checkNoFuyouUnionStatus(task.getMerchant_sn())) {
                    try {
                        Long queryTime = DefaultValueUtil.value(scheduleUtil.getQueryTime().getContractTemp(), ScheduleUtil.DEFAULT_THREE_HOURS_MILLIS_QUERY);
                        ContractSubTask unionSubTask = contractSubTaskMapper.getFuyouUnionTaskWithMerchantSn(task.getMerchant_sn(), StringUtil.formatDate(System.currentTimeMillis() - queryTime));
                        if (unionSubTask == null) {
                            addUnionTask(task);
                        }
                    } catch (Exception e) {
                        log.error("生成富友云闪付任务异常:merchantSn:{},taskId:{}", task.getMerchant_sn(), task.getId());
                    }
                } else {
                    if (contractSubTask.getStatus_influ_p_task() == 1 && SUPPORT_SYNC_UNION_PAY_ACQUIRER.contains(mcChannel.get().getAcquirer())) {
                        ContractSubTask queryResultSubTask = new ContractSubTask()
                                .setDefault_channel(0)
                                .setChange_config(0)
                                .setPayway(PaywayEnum.ACQUIRER.getValue())
                                .setMerchant_sn(task.getMerchant_sn())
                                .setChannel(mcChannel.get().getChannel())
                                .setTask_type(ProviderUtil.SUB_TASK_TASK_TYPE_UNION_MER_QUERY)
                                .setP_task_id(task.getId())
                                .setSchedule_dep_task_id(contractSubTask.getId())
                                .setSchedule_status(ScheduleEnum.SCHEDULE_ENABLE.getValue())
                                .setContract_rule(contractSubTask.getContract_rule())
                                .setRule_group_id(contractSubTask.getRule_group_id())
                                .setPriority(getSubTaskPriority());
                        contractSubTaskMapper.insert(queryResultSubTask);
                    }
                }
            }
        }
        //更新结果
        ContractTask update = new ContractTask()
                .setId(taskId)
                .setStatus(resultStatus);
        if (!StringUtil.empty(result)) {
            update.setResult(result);
        }
//        contractTaskMapper.updateByPrimaryKey(update);
        // TODO 更新contract_task表status,并根据type和status判断是否发送消息到神策
        contractTaskBiz.update(update);
    }


    private void addUnionTask(ContractTask contractTask){
        ContractSubTask subTask = new ContractSubTask()
                .setMerchant_sn(contractTask.getMerchant_sn())
                .setP_task_id(contractTask.getId())
                .setStatus_influ_p_task(0)
                .setChannel(ProviderUtil.FUYOU_CHANNEL)
                .setChange_config(0)
                .setTask_type(5)
                .setDefault_channel(1)
                .setPayway(PaywayEnum.UNIONPAY.getValue())
                .setContract_rule("fuyou-1038-17")
                .setRule_group_id(ProviderUtil.FUYOU_CHANNEL)
                .setSchedule_status(ScheduleEnum.SCHEDULE_ENABLE.getValue())
                .setRetry(0);
        contractSubTaskMapper.insert(subTask);
    }

    /**
     * 检查有没有富友云闪付参数
     * @param merchantSn
     * @return
     */
    private boolean checkNoFuyouUnionStatus(String merchantSn){
        MerchantProviderParamsExample unionDto = new MerchantProviderParamsExample();
        unionDto.createCriteria()
                .andMerchant_snEqualTo(merchantSn)
                .andPaywayEqualTo(PaywayEnum.UNIONPAY.getValue())
                .andProviderEqualTo(ProviderEnum.PROVIDER_FUYOU.getValue())
                .andDeletedEqualTo(false);
        List<MerchantProviderParams> params = merchantProviderParamsMapper.selectByExample(unionDto);
        return params == null || params.size() == 0;
    }

    public void checkAndSendTodo(Map contextParam) throws Exception{
        Map merchant = (Map) contextParam.get("merchant");
        Map license = (Map) contextParam.get("merchantBusinessLicense");
        Map bankAccount = (Map) contextParam.get("bankAccount");
        // 企业对私
        if (BeanUtil.getPropInt(license, "type", 0) >= BusinessLicenseTypeEnum.ENTERPRISE.getValue() &&
                BankAccountTypeEnum.isPersonal(BeanUtil.getPropInt(bankAccount, MerchantBankAccount.TYPE))
        ) {
            TodoAddReq req = new TodoAddReq();
            req.setTitle("富友采集商户对公凭证");
            req.setAppUrl("审批");
            req.setDescription("商户名:" + BeanUtil.getPropString(merchant, Merchant.NAME) + "，商户号:" + BeanUtil.getPropString(merchant, Merchant.SN)
                    + "，根据收单机构入网要求，该商户需在7日内尽快补充对公凭证，否则将影响商户间连扫码收款权益。请尽快点击下方去处理按钮或通过【富友企业对私商户补充对公凭证】审批完成资料提交，资料上传成功以审批通过状态为准。*注意：若商户无法补充对应资料，建议切换至其他收单通道。");
            UserEs userEs = aopBiz.getMaintainUser(BeanUtil.getPropString(merchant, "id"));
            Executor executor = new Executor();
            executor.setId(userEs.getId());
            executor.setName(userEs.getLinkman());
            List<Executor> executorList = new ArrayList<>();
            executorList.add(executor);
            req.setExecutors(executorList);
            req.setAppUrl(getFuyouApprovalUrl(userEs.getId()));
            Creator creator = new Creator();
            creator.setName("system");
            creator.setId("system");
            req.setCreator(creator);
            req.setPlatformCode("crm");
            LinkObject linkObject = new LinkObject();
            linkObject.setLinkObject("merchant");
            linkObject.setLinkObjectSn(BeanUtil.getPropString(merchant, "sn"));
            req.setSceneCode("merchant_public_certificate");
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.WEEK_OF_MONTH, 1);
            req.setExpiredTm(calendar.getTimeInMillis());
            req.setLinkObject(linkObject);
            todoDetailService.add(req);
        }
    }

    private String getFuyouApprovalUrl(String userId){
        AuditPreStartDto auditPreStartDto = new AuditPreStartDto();
        auditPreStartDto.setOperator(userId);
        auditPreStartDto.setPlatform("CRM");
        auditPreStartDto.setNoCheckInitiatorFlag(false);
        auditPreStartDto.setAuditTemplateId(Long.valueOf(fuyouApprovalTemplateId));
        return auditService.preStartAuditForInner("crmapp", auditPreStartDto);
    }

    private void handleContractTaskByMultiEvent(ContractTask contractTask, int resultStatus, String result, MultiProviderContractEvent contractEvent) {
        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(contractEvent.getMerchant_sn());
        // 成功
        if (resultStatus == TaskStatus.SUCCESS.getVal()) {
            // 主通道成功
            if (contractTask.getId().equals(contractEvent.getPrimary_task_id())) {
                // 主通道成功 并且contract_status不是成功，则设为默认去
                if (contractStatus.getStatus() != ContractStatus.STATUS_SUCCESS) {
                    setDefaultProvider(contractTask, result, resultStatus, contractEvent);
                }
            } else {
                ContractTask primaryContractTask = contractTaskMapper.selectByPrimaryKey(contractEvent.getPrimary_task_id());
                // 次通道成功 主通道失败 则将次通道设为默认
                if (TaskStatus.FAIL.getVal().equals(primaryContractTask.getStatus())) {
                    setDefaultProvider(contractTask, result, resultStatus, contractEvent);
                    return;
                }
                // 次通道成功 并且已经过了该渠道配置的时间 && contract_status不是成功 则设为默认去
                long timeAfterEventCreated = System.currentTimeMillis() - contractEvent.getCreate_at().getTime();
                if (contractStatus.getStatus() != ContractStatus.STATUS_SUCCESS && timeAfterEventCreated > multiEventBiz.getPrimaryContractTimeOutTime(contractEvent)) {
                    setDefaultProvider(contractTask, result, resultStatus, contractEvent);
                }
            }
        }
        // 失败
        if (resultStatus == TaskStatus.FAIL.getVal()) {
            ContractTask otherTask = getOtherOneContractTask(contractTask, contractEvent);
            // 如果不存在另外一个任务或者另外一个任务也是失败 直接失败就好
            if (otherTask == null || otherTask.getStatus().equals(TaskStatus.FAIL.getVal())) {
                setDefaultProvider(contractTask, result, TaskStatus.FAIL.getVal(), contractEvent);
            } else if (TaskStatus.SUCCESS.getVal().equals(otherTask.getStatus()) && contractStatus.getStatus() != ContractStatus.STATUS_SUCCESS) {
                // 如果另外一个任务是成功 并且contract_status不是成功
                setDefaultProvider(otherTask, result, TaskStatus.SUCCESS.getVal(), contractEvent);
            }
            // 如果另外一个任务是审核中 do nothing
        }

        //更新结果
        ContractTask update = new ContractTask()
                .setId(contractTask.getId())
                .setStatus(resultStatus);
        if (!StringUtil.empty(result)) {
            update.setResult(result);
        }
        contractTaskBiz.update(update);
    }

    private ContractTask getOtherOneContractTask(ContractTask contractTask, MultiProviderContractEvent contractEvent) {
        // 当前失败的任务是主通道的
        if (contractEvent.getPrimary_task_id().equals(contractTask.getId())) {
            return contractEvent.getSecondary_task_id() == null ? null : contractTaskMapper.selectByPrimaryKey(contractEvent.getSecondary_task_id());
        } else {
            return contractTaskMapper.selectByPrimaryKey(contractEvent.getPrimary_task_id());
        }
    }

    /**
     * 成功设置默认通道 发消息 提交实名 改银行卡状态 切通道
     * 失败 发消息 改银行卡状态
     *
     * @param contractTask
     * @param result
     * @param resultStatus
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setDefaultProvider(ContractTask contractTask, String result, int resultStatus, MultiProviderContractEvent contractEvent) {
        String merchantSn = contractTask.getMerchant_sn();
        Map context = JSON.parseObject(contractTask.getEvent_context(), Map.class);
        if (resultStatus == TaskStatus.SUCCESS.getVal()) {
            String memo = "SUCCESS";
            String acquirer = ruleContext.getRuleGroup(contractTask.getRule_group_id()).getAcquirer();
            dataBusBiz.insert(ContractStatus.STATUS_SUCCESS, merchantSn, memo, acquirer);
            bankCardService.updateCardAfterTaskStatus(context, MerchantBankAccount.VERIFY_STATUS_SUCC, memo);
            List<ContractSubTask> subTasks = contractSubTaskMapper.selectNetInSubTasksByMerchantSnAndRuleGroup(merchantSn, contractTask.getRule_group_id());
            for (ContractSubTask subTask : subTasks) {
                if (subTask.getChange_config() != null && subTask.getChange_config() == 1) {
                    if (WosaiStringUtils.isEmpty(subTask.getResponse_body())
                            //tonglianv2 没有云闪付商户号
                            || (ProviderUtil.TONGLIAN_V2_CHANNEL.equals(subTask.getChannel()) && PaywayEnum.UNIONPAY.getValue().equals(subTask.getPayway()))
                    ) {
                        continue;
                    }
                    PayWayConfigChange payWayConfigChange = new PayWayConfigChange().
                            setBody(subTask.getResponse_body())
                            .setMerchant_sn(merchantSn)
                            .setPayway(subTask.getPayway())
                            .setChannel(subTask.getChannel());
                    payWayConfigChangeMapper.insertSelective(payWayConfigChange);
                }
            }
            Map contextParam = JSON.parseObject(contractTask.getEvent_context(), Map.class);
            String merchantId = BeanUtil.getPropString(contextParam, "merchant.id");
            updateTradeParamsBiz.updateClearanceProvider(merchantId, acquirer);
        } else {
            String memo = MapUtils.getString(JSON.parseObject(result, Map.class), "result");
            dataBusBiz.insert(ContractStatus.STATUS_BIZ_FAIL, merchantSn, memo);
            bankCardService.updateCardAfterTaskStatus(context, MerchantBankAccount.VERIFY_STATUS_FAIL, memo);
        }
        MultiProviderContractEvent event = new MultiProviderContractEvent().setId(contractEvent.getId())
                .setStatus(resultStatus == TaskStatus.SUCCESS.getVal() ? MultiProviderContractEvent.STATUS_SUCCESS : MultiProviderContractEvent.STATUS_BIZ_FAIL);
        multiEventMapper.updateByPrimaryKeySelective(event);
    }

    private Date getSubTaskPriority() {
        Calendar instance = Calendar.getInstance();
        instance.setTime(new Date());
        instance.add(Calendar.MINUTE, 5);
        return instance.getTime();
    }

}
