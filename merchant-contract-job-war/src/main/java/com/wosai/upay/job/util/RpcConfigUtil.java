package com.wosai.upay.job.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean;
import com.wosai.data.jackson.RowDeserializerInstantiator;
import com.wosai.web.util.JsonUtil;

import java.util.HashMap;


/**
 * Created by lihebin on 2018/6/1.
 */
public class RpcConfigUtil {

    private static ObjectMapper snakeObjectMapper;

    static {
        snakeObjectMapper = JsonUtil.defaultRpcObjectMapper();
        snakeObjectMapper.setHandlerInstantiator(new RowDeserializerInstantiator());
    }


    public static JsonProxyFactoryBean getJsonProxyFactoryWareTracingBean(String serviceUrl, Class serviceInterface) {
        return getJsonProxyFactoryWareTracingBean(serviceUrl, serviceInterface, 1000, 3000);
    }

    public static JsonProxyFactoryBean getJsonProxyFactoryWareTracingMerchantContractBean(String serviceUrl, Class serviceInterface) {
        return getJsonProxyFactoryWareTracingBean(serviceUrl, serviceInterface, 1000, 120000);
    }

    public static JsonProxyFactoryBean getJsonProxyFactoryWareTracingBean(String serviceUrl, Class serviceInterface, int connectTimeOut, int readTimeOut) {
        JsonProxyFactoryBean factoryBean = new JsonProxyFactoryBean();
         HashMap<String, String> map = new HashMap<>();
         map.put("x-env-flag", "11601");
         factoryBean.setExtraHttpHeaders(map);
        factoryBean.setServiceUrl(serviceUrl);
        factoryBean.setServiceInterface(serviceInterface);
        factoryBean.setReadTimeoutMillis(readTimeOut);
        factoryBean.setConnectionTimeoutMillis(connectTimeOut);
        return factoryBean;
    }

    public static JsonProxyFactoryBean getJsonProxyFactoryBeanBySnakeCase(String serviceUrl, Class serviceInterface) {
        JsonProxyFactoryBean bean = getJsonProxyFactoryWareTracingBean(serviceUrl, serviceInterface);
        bean.setObjectMapper(snakeObjectMapper);
        return bean;
    }
}
